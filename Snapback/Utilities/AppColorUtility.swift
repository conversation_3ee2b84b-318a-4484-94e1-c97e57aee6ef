import SwiftUI

/// Utility class for generating consistent app colors across the application
class AppColorUtility {
    // Enhanced color palette for app windows - more distinct colors
    static let appColors: [Color] = [
        // Primary colors
        .blue,
        .red,
        .green,
        
        // Secondary colors
        .orange,
        .purple,
        .pink,
        
        // Tertiary colors
        .teal,
        .indigo,
        .cyan,
    ]
    
    /// Get a consistent color for an app based on its bundle ID and window info
    /// - Parameters:
    ///   - bundleID: The app's bundle identifier
    ///   - windowFrame: The window's frame (used to differentiate windows of the same app)
    ///   - opacity: Optional opacity to apply to the color (default: 1.0)
    /// - Returns: A consistent color for the app
    static func getAppColor(
        bundleID: String,
        windowFrame: CGRect,
        opacity: Double = 1.0
    ) -> Color {
        var hasher = Hasher()
        hasher.combine(bundleID)
        
        // Use window coordinates to differentiate windows of the same app
        hasher.combine(windowFrame.origin.x)
        hasher.combine(windowFrame.origin.y)
        hasher.combine(windowFrame.width)
        hasher.combine(windowFrame.height)
        
        let hash = abs(hasher.finalize())
        let baseColor = appColors[hash % appColors.count]
        
        return baseColor.opacity(opacity)
    }
    
    /// Get a consistent color for an app based on its WindowInfo
    /// - Parameters:
    ///   - windowInfo: The WindowInfo object containing app and window details
    ///   - opacity: Optional opacity to apply to the color (default: 1.0)
    /// - Returns: A consistent color for the app, or gray if no bundle ID is available
    static func getAppColor(
        for windowInfo: WindowInfo,
        opacity: Double = 1.0
    ) -> Color {
        guard let bundleID = windowInfo.appBundleIdentifier else {
            return Color.gray.opacity(opacity)
        }
        
        return getAppColor(
            bundleID: bundleID,
            windowFrame: windowInfo.frame,
            opacity: opacity
        )
    }
}
