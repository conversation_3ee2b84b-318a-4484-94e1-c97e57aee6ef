//
//  AppDelegate.swift
//  Snapback
//
//  Created by <PERSON> on 23/01/25.
//

import ApplicationServices
import Combine
import CoreGraphics
// Import logging services
import Foundation
import KeyboardShortcuts
import MASShortcut
import SwiftUI

class AppDelegate: NSObject, NSApplicationDelegate, ObservableObject {
    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "AppDelegate"

    var statusBarItem: NSStatusItem?
    let ownBundleID = Bundle.main.bundleIdentifier

    // Published property to track permission status
    @Published var isAccessibilityPermissionGranted: Bool = false

    private lazy var windowMover = WindowMover()
    private lazy var calculationService = WindowCalculationService()
    private lazy var screenDetection = ScreenDetectionService()

    private lazy var accessibilityElement = AccessibilityElement()
    private lazy var windowSnappingService = WindowSnappingService(
        windowMover: windowMover,
        calculationService: calculationService,
        screenDetection: screenDetection
    )

    @MainActor private lazy var snappingManager = SnappingManager(
        windowSnappingService: windowSnappingService)

    lazy var workspaceService = WorkspaceService(snappingService: windowSnappingService)
    lazy var shortcutService = ShortcutService(
        appDelegate: self,
        snappingService: windowSnappingService,
        workspaceService: workspaceService
    )
    // Make windowManager public so it can be accessed from other views
    lazy var windowManager = WindowManager(
        appDelegate: self,
        workspaceService: workspaceService
    )

    private var workspaceCancellables = Set<AnyCancellable>()
    private var loggingObservers: [NSObjectProtocol] = []

    func applicationDidFinishLaunching(_ notification: Notification) {
        logger.info("applicationDidFinishLaunching started", service: serviceName)
        NSApp.setActivationPolicy(.accessory)
        NSApp.activate(ignoringOtherApps: true)

        // Initialize status bar (but don't show menu yet)
        initializeStatusBar()

        // Initialize toast system
        initializeToastSystem()

        // Check accessibility permissions
        checkAndRequestPermissions()

        // Set up logging observers
        setupLoggingObservers()

        // Check for conflicting window management applications
        checkForConflictingApps()

        // Check for problematic applications
        checkForProblematicApps()

        // Set up notification observer for permission changes
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePermissionStatusChanged(_:)),
            name: PermissionManager.permissionStatusChanged,
            object: nil
        )

        // Observe menu refresh requests
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleRefreshStatusMenu(_:)),
            name: .refreshStatusMenu,
            object: nil
        )

        // Observe window management enabled/disabled changes
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleWindowManagementEnabledChanged(_:)),
            name: Notification.Name("WindowManagementEnabledChanged"),
            object: nil
        )

        // Migrate existing shortcuts to KeyboardShortcuts format
        shortcutService.migrateExistingShortcuts()

        // Log the migration
        logger.info("Migrated existing shortcuts to KeyboardShortcuts format", service: serviceName)

        // Make sure KeyboardShortcuts is enabled
        KeyboardShortcuts.isEnabled = true
        logger.info("KeyboardShortcuts.isEnabled set to true", service: serviceName)

        // Register keyboard shortcuts
        shortcutService.registerKeyboardShortcuts()

        // Initialize drag-to-snap with default settings if not already set
        let dragToSnapEnabled: Bool
        if UserDefaults.standard.object(forKey: "dragToSnapEnabled") == nil {
            UserDefaults.standard.set(true, forKey: "dragToSnapEnabled")
            dragToSnapEnabled = true
        } else {
            dragToSnapEnabled = UserDefaults.standard.bool(forKey: "dragToSnapEnabled")
        }

        // Set default value for allowAnyShortcut for new users
        if UserDefaults.standard.object(forKey: "allowAnyShortcut") == nil {
            // For new users, set allowAnyShortcut to true by default (like Rectangle)
            UserDefaults.standard.set(true, forKey: "allowAnyShortcut")
            logger.info("Set default allowAnyShortcut to true for new user", service: serviceName)
        }

        // Use Task to call the MainActor-isolated method
        Task { @MainActor in
            snappingManager.setEnabled(dragToSnapEnabled)
        }

        // Set up notification observers for drag-to-snap settings
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleDragToSnapEnabledChanged(_:)),
            name: Notification.Name("DragToSnapEnabledChanged"),
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleSnapModifiersChanged(_:)),
            name: Notification.Name("SnapModifiersChanged"),
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAvoidSystemConflictsChanged(_:)),
            name: Notification.Name("AvoidSystemConflictsChanged"),
            object: nil
        )

        workspaceService.$workspaces
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.logger.debug(
                    "Detected workspace change, rebuilding menu",
                    service: self?.serviceName ?? "AppDelegate")
                self?.setupMenu()
            }
            .store(in: &workspaceCancellables)

        workspaceService.$isRestoring
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.logger.debug(
                    "Detected isRestoring change, rebuilding menu",
                    service: self?.serviceName ?? "AppDelegate")
                self?.setupMenu()
            }
            .store(in: &workspaceCancellables)

        workspaceService.$restorationStatusMessage
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                if self?.workspaceService.isRestoring == true {
                    self?.logger.debug(
                        "Detected restorationStatusMessage change while restoring, rebuilding menu",
                        service: self?.serviceName ?? "AppDelegate"
                    )
                    self?.setupMenu()
                }
            }
            .store(in: &workspaceCancellables)

        // Add observer for explicit menu refresh requests
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleRefreshStatusMenu(_:)),
            name: Notification.Name("RefreshStatusMenu"),
            object: nil
        )

        // Add observer for KeyboardShortcuts library's shortcut changes
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleRefreshStatusMenu(_:)),
            name: Notification.Name("KeyboardShortcuts_shortcutByNameDidChange"),
            object: nil
        )

        logger.info("applicationDidFinishLaunching finished", service: serviceName)
    }

    func applicationShouldTerminate(_ sender: NSApplication) -> NSApplication.TerminateReply {
        logger.info(
            "applicationShouldTerminate called. Returning .terminateNow", service: serviceName)
        return .terminateNow
    }

    private func initializeStatusBar() {
        logger.info("Initializing status bar item", service: serviceName)
        statusBarItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.squareLength)

        guard let statusBarItem = statusBarItem else {
            logger.error("FATAL: Failed to create status bar item", service: serviceName)
            return
        }

        if let button = statusBarItem.button {
            // Try to load the custom SVG icon first
            if let customIcon = NSImage(named: "MenuIcon") {
                // Set proper size for menu bar (typically 16x16 or 18x18 points)
                customIcon.size = NSSize(width: 18, height: 18)
                customIcon.isTemplate = true
                button.image = customIcon
                logger.info(
                    "Successfully loaded custom MenuIcon with size \(customIcon.size), representations: \(customIcon.representations.count)",
                    service: serviceName)

                // Debug: Log the image representations
                for (index, rep) in customIcon.representations.enumerated() {
                    logger.debug(
                        "MenuIcon representation \(index): \(type(of: rep)), size: \(rep.size)",
                        service: serviceName)
                }
            } else {
                // Fallback to system symbol if custom icon fails
                button.image = NSImage(
                    systemSymbolName:
                        "inset.filled.leadinghalf.toptrailing.bottomtrailing.rectangle",
                    accessibilityDescription: "Snapback")
                if button.image == nil {
                    logger.warning(
                        "WARNING: Failed to load both custom and system symbol image",
                        service: serviceName)
                } else {
                    logger.info(
                        "Using fallback system symbol for menu bar icon", service: serviceName)
                }
            }
            button.toolTip = "Snapback"
        } else {
            logger.warning("WARNING: Status bar item button is nil", service: serviceName)
        }
        // Always show the menu bar icon (no longer configurable)
        statusBarItem.isVisible = true
    }

    func setupMenu() {
        logger.debug("setupMenu() called", service: serviceName, category: .userInterface)
        guard let statusBarItem = statusBarItem else {
            logger.warning(
                "statusBarItem is nil, cannot setup menu", service: serviceName,
                category: .userInterface)
            return
        }

        logger.debug("Building and setting menu", service: serviceName)
        logger.debug("Calling buildMenu()", service: serviceName, category: .userInterface)
        let menu = buildMenu()
        logger.debug(
            "buildMenu() returned, setting menu on statusBarItem", service: serviceName,
            category: .userInterface)
        statusBarItem.menu = menu
        logger.debug("Menu set on statusBarItem", service: serviceName, category: .userInterface)
    }

    deinit {
        logger.debug("Deinitializing", service: serviceName)
        workspaceCancellables.forEach { $0.cancel() }

        // Remove logging observers
        loggingObservers.forEach { NotificationCenter.default.removeObserver($0) }
    }

    // MARK: - Logging Observers

    private func setupLoggingObservers() {
        // Add observers for LoggingManager notifications
        let observer = NotificationCenter.default.addObserver(
            forName: LoggingManager.loggingSettingsChanged,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let key = notification.object as? String {
                self?.logger.debug(
                    "Logging setting changed: \(key)",
                    service: self?.serviceName ?? "AppDelegate")
                self?.setupMenu()  // Refresh menu to update checkmarks
            }
        }

        loggingObservers.append(observer)

        // Add debug log to verify observer is set up
        logger.debug(
            "Logging observer set up for notification: \(LoggingManager.loggingSettingsChanged.rawValue)",
            service: serviceName)

        logger.debug("Set up \(loggingObservers.count) logging observers", service: serviceName)
    }

    func buildMenu() -> NSMenu {
        logger.debug("buildMenu() called", service: serviceName, category: .userInterface)
        let menu = NSMenu()
        logger.debug("Created new menu", service: serviceName, category: .userInterface)

        func createMenuItem(
            title: String, action: Selector?, keyEquivalent: String = "",
            sfSymbolName: String? = nil, isEnabled: Bool = true
        ) -> NSMenuItem {
            let menuItem = NSMenuItem(title: title, action: action, keyEquivalent: keyEquivalent)
            if let symbolName = sfSymbolName {
                print("🔍 MENU DEBUG: Attempting to load symbol: \(symbolName)")

                // Try to load the symbol using the system symbol API
                if let image = NSImage(
                    systemSymbolName: symbolName, accessibilityDescription: title)
                {
                    image.isTemplate = true
                    menuItem.image = image
                    print("✅ MENU DEBUG: Successfully loaded symbol: \(symbolName)")
                } else {
                    print("⚠️ MENU DEBUG: Failed to load symbol: \(symbolName)")

                    // If it's a custom symbol, try with different formats
                    if symbolName.hasPrefix("Custom") {
                        // Try with dot notation format (custom.name)
                        let dotNotation = "custom." + symbolName.dropFirst(6).lowercased()
                        if let image = NSImage(
                            systemSymbolName: dotNotation, accessibilityDescription: title)
                        {
                            image.isTemplate = true
                            menuItem.image = image
                            print(
                                "✅ MENU DEBUG: Successfully loaded with dot notation: \(dotNotation)"
                            )
                        } else {
                            print("⚠️ MENU DEBUG: Failed with dot notation: \(dotNotation)")

                            // Try with named image as fallback
                            if let image = NSImage(named: symbolName) {
                                image.isTemplate = true
                                menuItem.image = image
                                print("✅ MENU DEBUG: Loaded as named image: \(symbolName)")
                            } else {
                                print("⚠️ MENU DEBUG: Failed to load as named image: \(symbolName)")

                                // Create a simple fallback image
                                let size = NSSize(width: 20, height: 20)
                                let image = NSImage(size: size)
                                image.lockFocus()

                                // Draw a simple rectangle
                                NSColor.textColor.set()
                                let rect = NSRect(x: 6, y: 4, width: 8, height: 12)
                                let path = NSBezierPath(rect: rect)
                                path.stroke()

                                image.unlockFocus()
                                image.isTemplate = true
                                menuItem.image = image
                                print("✅ MENU DEBUG: Created fallback image for: \(symbolName)")
                            }
                        }
                    }
                }
            }
            if action != nil && isEnabled {
                menuItem.target = (action == #selector(NSApplication.terminate(_:))) ? NSApp : self
            } else {
                menuItem.target = nil
                menuItem.action = nil
            }
            menuItem.isEnabled = isEnabled
            return menuItem
        }

        if workspaceService.isRestoring {
            let status =
                workspaceService.restorationStatusMessage.isEmpty
                ? "Restoring workspace..." : workspaceService.restorationStatusMessage
            menu.addItem(
                createMenuItem(
                    title: status, action: nil, sfSymbolName: "arrow.clockwise", isEnabled: false))
            menu.addItem(NSMenuItem.separator())
            menu.addItem(
                createMenuItem(
                    title: "Quit Snapback", action: #selector(NSApplication.terminate(_:)),
                    keyEquivalent: "q", sfSymbolName: "power"))
        } else {

            // Check if window management features are enabled
            let windowManagementEnabled = DefaultsManager.shared.windowManagementEnabled

            // Only add window management menu items if the feature is enabled
            if windowManagementEnabled {
                // Create menu items with updated keyboard shortcuts
                // Left Half
                let leftHalfItem = createMenuItem(
                    title: "Left Half", action: #selector(snapLeft), keyEquivalent: "",
                    sfSymbolName: "inset.filled.lefthalf.rectangle")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .leftHalf),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    logger.debug(
                        "Left Half shortcut key code: \(keyCode) (0x\(String(keyCode, radix: 16)))",
                        service: serviceName,
                        category: .shortcuts
                    )

                    // Special handling for arrow keys
                    if keyCode == 0x7B {  // Left arrow
                        leftHalfItem.keyEquivalent = "←"
                        leftHalfItem.keyEquivalentModifierMask = shortcut.modifiers
                        logger.debug(
                            "Set Left Half shortcut to left arrow with modifiers \(shortcut.modifiers.rawValue)",
                            service: serviceName,
                            category: .shortcuts
                        )
                    } else if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        leftHalfItem.keyEquivalent = keyChar.lowercased()
                        leftHalfItem.keyEquivalentModifierMask = shortcut.modifiers
                        logger.debug(
                            "Set Left Half shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)",
                            service: serviceName,
                            category: .shortcuts
                        )
                    } else {
                        // Fallback to default
                        leftHalfItem.keyEquivalent = "←"
                        leftHalfItem.keyEquivalentModifierMask = [.control, .option]
                        logger.warning(
                            "Using fallback shortcut for Left Half",
                            service: serviceName,
                            category: .shortcuts
                        )
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    leftHalfItem.keyEquivalent = ""
                    leftHalfItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Left Half, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(leftHalfItem)

                // Right Half
                let rightHalfItem = createMenuItem(
                    title: "Right Half", action: #selector(snapRight), keyEquivalent: "",
                    sfSymbolName: "inset.filled.righthalf.rectangle")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .rightHalf),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    print(
                        "🔍 MENU DEBUG: Right Half shortcut key code: \(keyCode) (0x\(String(keyCode, radix: 16)))"
                    )

                    // Special handling for arrow keys
                    if keyCode == 0x7C {  // Right arrow
                        rightHalfItem.keyEquivalent = "→"
                        rightHalfItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Right Half shortcut to right arrow with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        rightHalfItem.keyEquivalent = keyChar.lowercased()
                        rightHalfItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Right Half shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else {
                        // Fallback to default
                        rightHalfItem.keyEquivalent = "→"
                        rightHalfItem.keyEquivalentModifierMask = [.control, .option]
                        print("⚠️ MENU DEBUG: Using fallback shortcut for Right Half")
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    rightHalfItem.keyEquivalent = ""
                    rightHalfItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Right Half, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(rightHalfItem)

                // Top Half
                let topHalfItem = createMenuItem(
                    title: "Top Half", action: #selector(snapTopHalf), keyEquivalent: "",
                    sfSymbolName: "inset.filled.tophalf.rectangle")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .topHalf),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        topHalfItem.keyEquivalent = keyChar.lowercased()
                        topHalfItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Top Half shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else {
                        // Fallback to default
                        topHalfItem.keyEquivalent = "↑"
                        topHalfItem.keyEquivalentModifierMask = [.control, .option]
                        print("⚠️ MENU DEBUG: Using fallback shortcut for Top Half")
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    topHalfItem.keyEquivalent = ""
                    topHalfItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Top Half, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(topHalfItem)

                // Bottom Half
                let bottomHalfItem = createMenuItem(
                    title: "Bottom Half", action: #selector(snapBottomHalf), keyEquivalent: "",
                    sfSymbolName: "inset.filled.bottomhalf.rectangle")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .bottomHalf),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        bottomHalfItem.keyEquivalent = keyChar.lowercased()
                        bottomHalfItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Bottom Half shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else {
                        // Fallback to default
                        bottomHalfItem.keyEquivalent = "↓"
                        bottomHalfItem.keyEquivalentModifierMask = [.control, .option]
                        print("⚠️ MENU DEBUG: Using fallback shortcut for Bottom Half")
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    bottomHalfItem.keyEquivalent = ""
                    bottomHalfItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Bottom Half, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(bottomHalfItem)
                menu.addItem(NSMenuItem.separator())

                // Quarters with updated keyboard shortcuts
                // Top Left Quarter
                let topLeftQuarterItem = createMenuItem(
                    title: "Top Left Quarter", action: #selector(snapTopLeftQuarter),
                    keyEquivalent: "", sfSymbolName: "rectangle.inset.topleft.filled")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .topLeftQuarter),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        topLeftQuarterItem.keyEquivalent = keyChar.lowercased()
                        topLeftQuarterItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Top Left Quarter shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else {
                        // Fallback to default
                        topLeftQuarterItem.keyEquivalent = "u"
                        topLeftQuarterItem.keyEquivalentModifierMask = [.control, .option]
                        print("⚠️ MENU DEBUG: Using fallback shortcut for Top Left Quarter")
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    topLeftQuarterItem.keyEquivalent = ""
                    topLeftQuarterItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Top Left Quarter, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(topLeftQuarterItem)

                // Top Right Quarter
                let topRightQuarterItem = createMenuItem(
                    title: "Top Right Quarter", action: #selector(snapTopRightQuarter),
                    keyEquivalent: "", sfSymbolName: "rectangle.inset.topright.filled")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .topRightQuarter),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        topRightQuarterItem.keyEquivalent = keyChar.lowercased()
                        topRightQuarterItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Top Right Quarter shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else {
                        // Fallback to default
                        topRightQuarterItem.keyEquivalent = "i"
                        topRightQuarterItem.keyEquivalentModifierMask = [.control, .option]
                        print("⚠️ MENU DEBUG: Using fallback shortcut for Top Right Quarter")
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    topRightQuarterItem.keyEquivalent = ""
                    topRightQuarterItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Top Right Quarter, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(topRightQuarterItem)

                // Bottom Left Quarter
                let bottomLeftQuarterItem = createMenuItem(
                    title: "Bottom Left Quarter", action: #selector(snapBottomLeftQuarter),
                    keyEquivalent: "", sfSymbolName: "rectangle.inset.bottomleft.filled")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .bottomLeftQuarter),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        bottomLeftQuarterItem.keyEquivalent = keyChar.lowercased()
                        bottomLeftQuarterItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Bottom Left Quarter shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else {
                        // Fallback to default
                        bottomLeftQuarterItem.keyEquivalent = "j"
                        bottomLeftQuarterItem.keyEquivalentModifierMask = [.control, .option]
                        print("⚠️ MENU DEBUG: Using fallback shortcut for Bottom Left Quarter")
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    bottomLeftQuarterItem.keyEquivalent = ""
                    bottomLeftQuarterItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Bottom Left Quarter, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(bottomLeftQuarterItem)

                // Bottom Right Quarter
                let bottomRightQuarterItem = createMenuItem(
                    title: "Bottom Right Quarter", action: #selector(snapBottomRightQuarter),
                    keyEquivalent: "", sfSymbolName: "rectangle.inset.bottomright.filled")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .bottomRightQuarter),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        bottomRightQuarterItem.keyEquivalent = keyChar.lowercased()
                        bottomRightQuarterItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Bottom Right Quarter shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else {
                        // Fallback to default
                        bottomRightQuarterItem.keyEquivalent = "k"
                        bottomRightQuarterItem.keyEquivalentModifierMask = [.control, .option]
                        print("⚠️ MENU DEBUG: Using fallback shortcut for Bottom Right Quarter")
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    bottomRightQuarterItem.keyEquivalent = ""
                    bottomRightQuarterItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Bottom Right Quarter, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(bottomRightQuarterItem)
                menu.addItem(NSMenuItem.separator())

                // Thirds with updated keyboard shortcuts
                // Left Third
                let leftThirdItem = createMenuItem(
                    title: "Left Third", action: #selector(snapLeftThird), keyEquivalent: "",
                    sfSymbolName: "rectangle.leadingthird.inset.filled")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .leftThird),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        leftThirdItem.keyEquivalent = keyChar.lowercased()
                        leftThirdItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Left Third shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else {
                        // Fallback to default
                        leftThirdItem.keyEquivalent = "d"
                        leftThirdItem.keyEquivalentModifierMask = [.control, .option]
                        print("⚠️ MENU DEBUG: Using fallback shortcut for Left Third")
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    leftThirdItem.keyEquivalent = ""
                    leftThirdItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Left Third, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(leftThirdItem)

                // Center Third
                let centerThirdItem = createMenuItem(
                    title: "Center Third", action: #selector(snapCenterThird), keyEquivalent: "",
                    sfSymbolName: "CustomCenterThird")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .centerThird),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        centerThirdItem.keyEquivalent = keyChar.lowercased()
                        centerThirdItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Center Third shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else {
                        // Fallback to default
                        centerThirdItem.keyEquivalent = "f"
                        centerThirdItem.keyEquivalentModifierMask = [.control, .option]
                        print("⚠️ MENU DEBUG: Using fallback shortcut for Center Third")
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    centerThirdItem.keyEquivalent = ""
                    centerThirdItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Center Third, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(centerThirdItem)

                // Right Third
                let rightThirdItem = createMenuItem(
                    title: "Right Third", action: #selector(snapRightThird), keyEquivalent: "",
                    sfSymbolName: "rectangle.trailingthird.inset.filled")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .rightThird),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        rightThirdItem.keyEquivalent = keyChar.lowercased()
                        rightThirdItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Right Third shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else {
                        // Fallback to default
                        rightThirdItem.keyEquivalent = "g"
                        rightThirdItem.keyEquivalentModifierMask = [.control, .option]
                        print("⚠️ MENU DEBUG: Using fallback shortcut for Right Third")
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    rightThirdItem.keyEquivalent = ""
                    rightThirdItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Right Third, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(rightThirdItem)
                menu.addItem(NSMenuItem.separator())

                // Two Thirds with updated keyboard shortcuts
                // Left Two Thirds
                let leftTwoThirdsItem = createMenuItem(
                    title: "Left Two Thirds", action: #selector(snapLeftTwoThirds),
                    keyEquivalent: "", sfSymbolName: "CustomLeftTwoThirds")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .leftTwoThirds),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        leftTwoThirdsItem.keyEquivalent = keyChar.lowercased()
                        leftTwoThirdsItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Left Two Thirds shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else {
                        // Fallback to default
                        leftTwoThirdsItem.keyEquivalent = "e"
                        leftTwoThirdsItem.keyEquivalentModifierMask = [.control, .option]
                        print("⚠️ MENU DEBUG: Using fallback shortcut for Left Two Thirds")
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    leftTwoThirdsItem.keyEquivalent = ""
                    leftTwoThirdsItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Left Two Thirds, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(leftTwoThirdsItem)

                // Center Two Thirds
                let centerTwoThirdsItem = createMenuItem(
                    title: "Center Two Thirds", action: #selector(snapCenterTwoThirds),
                    keyEquivalent: "", sfSymbolName: "CustomCenterTwoThirds")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .centerTwoThirds),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        centerTwoThirdsItem.keyEquivalent = keyChar.lowercased()
                        centerTwoThirdsItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Center Two Thirds shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else {
                        // Fallback to default
                        centerTwoThirdsItem.keyEquivalent = "r"
                        centerTwoThirdsItem.keyEquivalentModifierMask = [.control, .option]
                        print("⚠️ MENU DEBUG: Using fallback shortcut for Center Two Thirds")
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    centerTwoThirdsItem.keyEquivalent = ""
                    centerTwoThirdsItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Center Two Thirds, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(centerTwoThirdsItem)

                // Right Two Thirds
                let rightTwoThirdsItem = createMenuItem(
                    title: "Right Two Thirds", action: #selector(snapRightTwoThirds),
                    keyEquivalent: "", sfSymbolName: "CustomRightTwoThirds")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .rightTwoThirds),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        rightTwoThirdsItem.keyEquivalent = keyChar.lowercased()
                        rightTwoThirdsItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Right Two Thirds shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else {
                        // Fallback to default
                        rightTwoThirdsItem.keyEquivalent = "t"
                        rightTwoThirdsItem.keyEquivalentModifierMask = [.control, .option]
                        print("⚠️ MENU DEBUG: Using fallback shortcut for Right Two Thirds")
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    rightTwoThirdsItem.keyEquivalent = ""
                    rightTwoThirdsItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Right Two Thirds, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(rightTwoThirdsItem)
                menu.addItem(NSMenuItem.separator())

                // Fullscreen with updated keyboard shortcut
                let fullscreenItem = createMenuItem(
                    title: "Fullscreen", action: #selector(snapFullscreen), keyEquivalent: "",
                    sfSymbolName: "inset.filled.rectangle")

                // Get the shortcut from KeyboardShortcuts
                if let shortcut = KeyboardShortcuts.getShortcut(for: .fullscreen),
                    let key = shortcut.key
                {
                    let keyCode = UInt16(key.rawValue)
                    if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                        fullscreenItem.keyEquivalent = keyChar.lowercased()
                        fullscreenItem.keyEquivalentModifierMask = shortcut.modifiers
                        print(
                            "✅ MENU DEBUG: Set Fullscreen shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                        )
                    } else {
                        // Fallback to default
                        fullscreenItem.keyEquivalent = "\r"
                        fullscreenItem.keyEquivalentModifierMask = [.control, .option]
                        print("⚠️ MENU DEBUG: Using fallback shortcut for Fullscreen")
                    }
                } else {
                    // Don't show any shortcut in the menu when it's deleted
                    fullscreenItem.keyEquivalent = ""
                    fullscreenItem.keyEquivalentModifierMask = []
                    logger.debug(
                        "No shortcut found in KeyboardShortcuts for Fullscreen, showing blank shortcut in menu",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
                menu.addItem(fullscreenItem)
                menu.addItem(NSMenuItem.separator())

            }  // End of window management features conditional

            // Save Workspace option
            let saveItem = createMenuItem(
                title: "Save Workspace", action: #selector(saveCurrentWorkspace),
                keyEquivalent: "", sfSymbolName: "square.and.arrow.down")

            // Set the shortcut from KeyboardShortcuts if available
            logger.debug(
                "Checking for Save Workspace shortcut in KeyboardShortcuts",
                service: serviceName,
                category: .shortcuts
            )

            if let shortcut = KeyboardShortcuts.getShortcut(for: .saveWorkspace),
                let key = shortcut.key
            {
                logger.debug(
                    "Found Save Workspace shortcut: key=\(key.rawValue), modifiers=\(shortcut.modifiers.rawValue)",
                    service: serviceName,
                    category: .shortcuts
                )

                let keyCode = UInt16(key.rawValue)
                if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                    saveItem.keyEquivalent = keyChar.lowercased()
                    saveItem.keyEquivalentModifierMask = shortcut.modifiers
                    logger.debug(
                        "Set Save Workspace menu shortcut: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)",
                        service: serviceName,
                        category: .shortcuts
                    )
                } else {
                    // Fallback to default
                    saveItem.keyEquivalent = "s"
                    saveItem.keyEquivalentModifierMask = [.control, .option]
                    logger.debug(
                        "Using fallback shortcut for Save Workspace",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
            } else {
                // Don't show any shortcut in the menu when it's not set
                saveItem.keyEquivalent = ""
                saveItem.keyEquivalentModifierMask = []
                logger.debug(
                    "No shortcut found in KeyboardShortcuts for Save Workspace, showing blank shortcut in menu",
                    service: serviceName,
                    category: .shortcuts
                )
            }
            menu.addItem(saveItem)
            menu.addItem(NSMenuItem.separator())

            // Workspaces
            let currentWorkspaces = workspaceService.workspaces
            if !currentWorkspaces.isEmpty {
                // Log all workspaces and their shortcuts for debugging
                logger.info(
                    "Displaying \(currentWorkspaces.count) workspaces in menu", service: serviceName
                )

                // Print to console for immediate feedback
                print(
                    "🔍 WORKSPACE DEBUG: Found \(currentWorkspaces.count) workspaces to display in menu"
                )
                for (index, workspace) in currentWorkspaces.enumerated() {
                    // Log detailed information about each workspace
                    logger.info(
                        "Workspace [\(index)]: '\(workspace.name)', ID: \(workspace.id?.uuidString ?? "nil")",
                        service: serviceName)

                    // Print to console for immediate feedback
                    print(
                        "🔍 WORKSPACE [\(index)]: '\(workspace.name)', ID: \(workspace.id?.uuidString ?? "nil")"
                    )

                    if let keyCode = workspace.shortcutKeyCode,
                        let modifiers = workspace.shortcutModifiers
                    {
                        logger.info(
                            "  Has shortcut: keyCode=\(keyCode), modifiers=\(modifiers)",
                            service: serviceName)
                        logger.info(
                            "  Display string: \(workspace.shortcutDisplayString ?? "nil")",
                            service: serviceName)

                        // Print to console for immediate feedback
                        print(
                            "  🔑 Has shortcut: keyCode=\(keyCode) (0x\(String(keyCode, radix: 16))), modifiers=\(modifiers) (0x\(String(modifiers, radix: 16)))"
                        )
                        print("  📝 Display string: \(workspace.shortcutDisplayString ?? "nil")")

                        // Try to manually format the shortcut for debugging
                        let modifierFlags = NSEvent.ModifierFlags(rawValue: modifiers)
                        var displayString = ""

                        // Add modifier symbols
                        if modifierFlags.contains(.control) { displayString += "⌃" }
                        if modifierFlags.contains(.option) { displayString += "⌥" }
                        if modifierFlags.contains(.shift) { displayString += "⇧" }
                        if modifierFlags.contains(.command) { displayString += "⌘" }

                        // Add key character
                        if let keyChar = keycodeToString(keyCode) {
                            displayString += keyChar.uppercased()
                            print("  🔠 Manually formatted shortcut: \(displayString)")
                        } else {
                            print("  ⚠️ Could not convert keyCode \(keyCode) to character")
                        }

                        // Check if we can get the workspace ID
                        if let id = workspace.id {
                            logger.info(
                                "  Workspace has ID: \(id.uuidString)", service: serviceName)

                            // Check if the shortcut is registered in KeyboardShortcuts
                            let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)
                            if let shortcut = KeyboardShortcuts.getShortcut(for: shortcutName) {
                                print(
                                    "  ✅ Shortcut is registered in KeyboardShortcuts: \(shortcut)")
                            } else {
                                print("  ❌ Shortcut is NOT registered in KeyboardShortcuts")
                            }
                        }
                    } else {
                        logger.info("  No shortcut assigned", service: serviceName)
                        print("  ℹ️ No shortcut assigned")
                    }
                }

                // Add a "Workspaces" header item
                let workspacesHeaderItem = NSMenuItem(
                    title: "Workspaces", action: nil, keyEquivalent: "")
                menu.addItem(workspacesHeaderItem)

                // Check if we need to use a submenu (more than 6 workspaces)
                let maxWorkspacesInMainMenu = 6

                if currentWorkspaces.count > maxWorkspacesInMainMenu {
                    // Create a submenu for workspaces
                    let workspacesSubmenu = NSMenu()
                    let moreWorkspacesItem = createMenuItem(
                        title: "More Workspaces",
                        action: nil,
                        keyEquivalent: "",
                        sfSymbolName: "ellipsis.circle"
                    )
                    moreWorkspacesItem.submenu = workspacesSubmenu

                    // Add the first 6 workspaces directly to the main menu
                    for i in 0..<maxWorkspacesInMainMenu {
                        let workspace = currentWorkspaces[i]
                        // Create the menu item with just the workspace name
                        let wsMenuItem = createMenuItem(
                            title: workspace.name,
                            action: #selector(restoreSavedWorkspaceMenuItem(_:)),
                            keyEquivalent: "",
                            sfSymbolName: "macwindow"
                        )

                        // If there's a workspace ID, check for shortcut in KeyboardShortcuts library
                        if let id = workspace.id {
                            print(
                                "🔍 MENU DEBUG: Processing workspace '\(workspace.name)' with ID \(id.uuidString)"
                            )

                            // Get the shortcut from KeyboardShortcuts library
                            let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)
                            print("🔍 MENU DEBUG: Looking for shortcut with name: \(shortcutName)")

                            if let shortcut = KeyboardShortcuts.getShortcut(for: shortcutName) {
                                print(
                                    "🔍 MENU DEBUG: Found shortcut in KeyboardShortcuts: \(shortcut)"
                                )

                                if let key = shortcut.key {
                                    // Log the shortcut information from KeyboardShortcuts
                                    logger.debug(
                                        "Found shortcut in KeyboardShortcuts for workspace '\(workspace.name)': key=\(key.rawValue), modifiers=\(shortcut.modifiers.rawValue)",
                                        service: serviceName)

                                    print(
                                        "🔍 MENU DEBUG: Key raw value: \(key.rawValue), Modifiers raw value: \(shortcut.modifiers.rawValue)"
                                    )

                                    // Convert the key to a character for the menu item
                                    // First try to use our keycodeToString helper
                                    let keyCode = UInt16(key.rawValue)
                                    print("🔍 MENU DEBUG: Converted key to keyCode: \(keyCode)")

                                    if let keyChar = keycodeToString(keyCode) {
                                        print(
                                            "🔍 MENU DEBUG: Converted keyCode to character: '\(keyChar)'"
                                        )

                                        if keyChar.count == 1 {
                                            wsMenuItem.keyEquivalent = keyChar.lowercased()
                                            wsMenuItem.keyEquivalentModifierMask =
                                                shortcut.modifiers

                                            print(
                                                "✅ MENU DEBUG: Set menu shortcut for '\(workspace.name)' from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                                            )
                                            print(
                                                "✅ MENU DEBUG: Menu item now has keyEquivalent: '\(wsMenuItem.keyEquivalent)' and modifiers: \(wsMenuItem.keyEquivalentModifierMask.rawValue)"
                                            )
                                        } else {
                                            print(
                                                "⚠️ MENU DEBUG: Character '\(keyChar)' is not a single character, can't use for menu shortcut"
                                            )
                                        }
                                    } else {
                                        print(
                                            "⚠️ MENU DEBUG: Failed to convert keyCode \(keyCode) to character"
                                        )
                                    }
                                } else {
                                    print("⚠️ MENU DEBUG: Shortcut has no key: \(shortcut)")
                                }
                            } else {
                                print(
                                    "⚠️ MENU DEBUG: No shortcut found in KeyboardShortcuts for name: \(shortcutName)"
                                )

                                // Fall back to workspace model if KeyboardShortcuts doesn't have it
                                if let keyCode = workspace.shortcutKeyCode,
                                    let modifiers = workspace.shortcutModifiers
                                {
                                    print(
                                        "🔍 MENU DEBUG: Using fallback from workspace model: keyCode=\(keyCode), modifiers=\(modifiers)"
                                    )

                                    // Try to convert keyCode to character for the menu item
                                    if let keyChar = keycodeToString(keyCode) {
                                        print(
                                            "🔍 MENU DEBUG: Converted fallback keyCode to character: '\(keyChar)'"
                                        )

                                        if keyChar.count == 1 {
                                            wsMenuItem.keyEquivalent = keyChar.lowercased()
                                            wsMenuItem.keyEquivalentModifierMask =
                                                NSEvent.ModifierFlags(
                                                    rawValue: modifiers)

                                            print(
                                                "✅ MENU DEBUG: Set menu shortcut from workspace model: '\(keyChar)' with modifiers \(modifiers)"
                                            )
                                            print(
                                                "✅ MENU DEBUG: Menu item now has keyEquivalent: '\(wsMenuItem.keyEquivalent)' and modifiers: \(wsMenuItem.keyEquivalentModifierMask.rawValue)"
                                            )
                                        } else {
                                            print(
                                                "⚠️ MENU DEBUG: Character '\(keyChar)' is not a single character, can't use for menu shortcut"
                                            )
                                        }
                                    } else {
                                        print(
                                            "⚠️ MENU DEBUG: Failed to convert keyCode \(keyCode) to character"
                                        )
                                    }
                                } else {
                                    print(
                                        "ℹ️ MENU DEBUG: No shortcut data in workspace model either")
                                }
                            }

                            // Don't add the shortcut to the title since it's already shown on the right
                            wsMenuItem.title = workspace.name
                        }

                        wsMenuItem.representedObject = workspace
                        menu.addItem(wsMenuItem)

                        if workspace.customLayout != nil {
                            let layoutMenuItem = createMenuItem(
                                title: "Apply Layout: \(workspace.customLayout!.name)",
                                action: nil)
                            layoutMenuItem.representedObject = workspace
                            layoutMenuItem.indentationLevel = 1
                            menu.addItem(layoutMenuItem)
                        }
                    }

                    // Add the remaining workspaces to the submenu
                    for i in maxWorkspacesInMainMenu..<currentWorkspaces.count {
                        let workspace = currentWorkspaces[i]
                        // Create the menu item with just the workspace name
                        let wsMenuItem = NSMenuItem(
                            title: workspace.name,
                            action: #selector(restoreSavedWorkspaceMenuItem(_:)),
                            keyEquivalent: ""
                        )
                        wsMenuItem.target = self
                        if let image = NSImage(
                            systemSymbolName: "macwindow", accessibilityDescription: nil)
                        {
                            image.isTemplate = true
                            wsMenuItem.image = image
                        }

                        // If there's a workspace ID, check for shortcut in KeyboardShortcuts library
                        if let id = workspace.id {
                            // Get the shortcut from KeyboardShortcuts library
                            let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)
                            if let shortcut = KeyboardShortcuts.getShortcut(for: shortcutName),
                                let key = shortcut.key
                            {
                                // Log the shortcut information from KeyboardShortcuts
                                logger.debug(
                                    "Found shortcut in KeyboardShortcuts for workspace '\(workspace.name)': key=\(key.rawValue), modifiers=\(shortcut.modifiers.rawValue)",
                                    service: serviceName)

                                // Convert the key to a character for the menu item
                                let keyCode = UInt16(key.rawValue)
                                if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                                    wsMenuItem.keyEquivalent = keyChar.lowercased()
                                    wsMenuItem.keyEquivalentModifierMask = shortcut.modifiers
                                    logger.info(
                                        "Set menu shortcut from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)",
                                        service: serviceName)

                                    // Print to console for immediate feedback
                                    print(
                                        "🔵 Set menu shortcut for '\(workspace.name)' from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)"
                                    )
                                } else {
                                    logger.warning(
                                        "Failed to convert key \(key.rawValue) to a usable character for menu shortcut",
                                        service: serviceName)
                                    print(
                                        "⚠️ Failed to convert key \(key.rawValue) to a usable character for menu shortcut"
                                    )
                                }
                            } else if let keyCode = workspace.shortcutKeyCode,
                                let modifiers = workspace.shortcutModifiers
                            {
                                // Fall back to workspace model if KeyboardShortcuts doesn't have it
                                logger.debug(
                                    "Using fallback shortcut from workspace model: keyCode=\(keyCode), modifiers=\(modifiers)",
                                    service: serviceName)

                                // Try to convert keyCode to character for the menu item
                                if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                                    wsMenuItem.keyEquivalent = keyChar.lowercased()
                                    wsMenuItem.keyEquivalentModifierMask = NSEvent.ModifierFlags(
                                        rawValue: modifiers)
                                    logger.info(
                                        "Set menu shortcut from workspace model: '\(keyChar)' with modifiers \(modifiers)",
                                        service: serviceName)

                                    print(
                                        "⚠️ Using fallback shortcut from workspace model for '\(workspace.name)': '\(keyChar)' with modifiers \(modifiers)"
                                    )
                                } else {
                                    logger.warning(
                                        "Failed to convert keyCode \(keyCode) to a usable character for menu shortcut",
                                        service: serviceName)
                                    print(
                                        "⚠️ Failed to convert keyCode \(keyCode) to a usable character for menu shortcut"
                                    )
                                }
                            }

                            // Don't add the shortcut to the title since it's already shown on the right
                            wsMenuItem.title = workspace.name
                            logger.info(
                                "Set menu title for workspace: '\(workspace.name)'",
                                service: serviceName
                            )
                        }

                        wsMenuItem.representedObject = workspace
                        workspacesSubmenu.addItem(wsMenuItem)

                        if workspace.customLayout != nil {
                            let layoutMenuItem = NSMenuItem(
                                title: "Apply Layout: \(workspace.customLayout!.name)",
                                action: #selector(applySavedCustomLayoutMenuItem(_:)),
                                keyEquivalent: ""
                            )
                            layoutMenuItem.target = self
                            layoutMenuItem.representedObject = workspace
                            layoutMenuItem.indentationLevel = 1
                            workspacesSubmenu.addItem(layoutMenuItem)
                        }
                    }

                    // Add the "More Workspaces" submenu to the main menu
                    menu.addItem(moreWorkspacesItem)
                } else {
                    // If we have 6 or fewer workspaces, add them all directly to the main menu
                    for workspace in currentWorkspaces {
                        // Create the menu item with just the workspace name
                        let wsMenuItem = NSMenuItem(
                            title: workspace.name,
                            action: #selector(restoreSavedWorkspaceMenuItem(_:)),
                            keyEquivalent: ""
                        )
                        wsMenuItem.target = self
                        if let image = NSImage(
                            systemSymbolName: "macwindow", accessibilityDescription: nil)
                        {
                            image.isTemplate = true
                            wsMenuItem.image = image
                        }

                        // If there's a workspace ID, check for shortcut in KeyboardShortcuts library
                        if let id = workspace.id {
                            // Get the shortcut from KeyboardShortcuts library
                            let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)
                            if let shortcut = KeyboardShortcuts.getShortcut(for: shortcutName),
                                let key = shortcut.key
                            {
                                // Convert the key to a character for the menu item
                                let keyCode = UInt16(key.rawValue)
                                if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                                    wsMenuItem.keyEquivalent = keyChar.lowercased()
                                    wsMenuItem.keyEquivalentModifierMask = shortcut.modifiers

                                    logger.debug(
                                        "Set menu shortcut for '\(workspace.name)' from KeyboardShortcuts: '\(keyChar)' with modifiers \(shortcut.modifiers.rawValue)",
                                        service: serviceName)
                                }
                            } else if let keyCode = workspace.shortcutKeyCode,
                                let modifiers = workspace.shortcutModifiers
                            {
                                // Fall back to workspace model if KeyboardShortcuts doesn't have it
                                if let keyChar = keycodeToString(keyCode), keyChar.count == 1 {
                                    wsMenuItem.keyEquivalent = keyChar.lowercased()
                                    wsMenuItem.keyEquivalentModifierMask = NSEvent.ModifierFlags(
                                        rawValue: modifiers)

                                    logger.debug(
                                        "Set menu shortcut for '\(workspace.name)' from workspace model: '\(keyChar)' with modifiers \(modifiers)",
                                        service: serviceName)
                                }
                            }

                            // Don't add the shortcut to the title since it's already shown on the right
                            wsMenuItem.title = workspace.name
                        }

                        wsMenuItem.representedObject = workspace
                        menu.addItem(wsMenuItem)

                        if workspace.customLayout != nil {
                            let layoutMenuItem = NSMenuItem(
                                title: "Apply Layout: \(workspace.customLayout!.name)",
                                action: #selector(applySavedCustomLayoutMenuItem(_:)),
                                keyEquivalent: ""
                            )
                            layoutMenuItem.target = self
                            layoutMenuItem.representedObject = workspace
                            layoutMenuItem.indentationLevel = 1
                            menu.addItem(layoutMenuItem)
                        }
                    }
                }
                menu.addItem(NSMenuItem.separator())
            }

            menu.addItem(NSMenuItem.separator())

            // Logging options - only show in DEBUG builds
            #if DEBUG
                // Logging presets
                let loggingService = LoggingService.shared
                let currentLevel = loggingService.minimumLogLevel

                // Create main logging submenu
                let mainLoggingSubmenu = NSMenu()

                // Add header item
                mainLoggingSubmenu.addItem(
                    createMenuItem(title: "Debug Logging", action: nil, isEnabled: false))

                // Window Management Debugging
                let windowManagementItem = createMenuItem(
                    title: "Window Snapping Debugging",
                    action: #selector(enableWindowSnappingLogging),
                    sfSymbolName: "inset.filled.leadinghalf.toptrailing.bottomtrailing.rectangle")

                // Check if current settings match window management debugging preset
                if currentLevel == .debug && loggingService.isCategoryEnabled(.windowSnapping)
                    && loggingService.isCategoryEnabled(.windowPositioning)
                    && loggingService.isCategoryEnabled(.windowCalculation)
                    && !loggingService.isCategoryEnabled(.screenDetection)
                    && !loggingService.isCategoryEnabled(.screenDebug)
                {
                    windowManagementItem.state = .on
                }
                mainLoggingSubmenu.addItem(windowManagementItem)

                // Screen Detection Debugging
                let screenDetectionItem = createMenuItem(
                    title: "Screen Detection Debugging",
                    action: #selector(enableScreenDetectionLogging),
                    sfSymbolName: "display")

                // Check if current settings match screen detection debugging preset
                if currentLevel == .debug && loggingService.isCategoryEnabled(.screenDetection)
                    && loggingService.isCategoryEnabled(.screenDebug)
                    && !loggingService.isCategoryEnabled(.windowSnapping)
                    && !loggingService.isCategoryEnabled(.windowPositioning)
                    && !loggingService.isCategoryEnabled(.windowCalculation)
                {
                    screenDetectionItem.state = .on
                }
                mainLoggingSubmenu.addItem(screenDetectionItem)

                // Workspaces Debugging
                let mainWorkspacesItem = createMenuItem(
                    title: "Workspaces Debugging",
                    action: #selector(enableWorkspacesLogging),
                    sfSymbolName: "rectangle.on.rectangle")

                // Check if current settings match workspaces debugging preset
                if currentLevel == .debug && loggingService.isCategoryEnabled(.workspaces)
                    && !loggingService.isCategoryEnabled(.windowSnapping)
                    && !loggingService.isCategoryEnabled(.screenDetection)
                {
                    mainWorkspacesItem.state = .on
                }
                mainLoggingSubmenu.addItem(mainWorkspacesItem)

                // Workspace Preview Debugging
                let mainWorkspacePreviewItem = createMenuItem(
                    title: "Workspace Preview Debugging",
                    action: #selector(enableWorkspacePreviewLogging),
                    sfSymbolName: "rectangle.grid.2x2.fill")

                // Workspace preview debugging preset check removed
                // Always set to off since the feature is removed
                mainWorkspacePreviewItem.state = .off
                mainLoggingSubmenu.addItem(mainWorkspacePreviewItem)

                // General Usage (Less Verbose)
                let generalUsageItem = createMenuItem(
                    title: "General Usage (Less Verbose)",
                    action: #selector(enableGeneralLogging),
                    sfSymbolName: "text.bubble")

                // Check if current settings match general usage preset
                if currentLevel == .info
                    && LogCategory.allCases.allSatisfy({ loggingService.isCategoryEnabled($0) })
                {
                    generalUsageItem.state = .on
                }
                mainLoggingSubmenu.addItem(generalUsageItem)

                // Warnings & Errors Only
                let warningsOnlyItem = createMenuItem(
                    title: "Warnings & Errors Only",
                    action: #selector(enableWarningsAndErrorsOnly),
                    sfSymbolName: "exclamationmark.triangle")

                // Check if current settings match warnings & errors only preset
                if currentLevel == .warning
                    && LogCategory.allCases.allSatisfy({ loggingService.isCategoryEnabled($0) })
                {
                    warningsOnlyItem.state = .on
                }
                mainLoggingSubmenu.addItem(warningsOnlyItem)

                // All Logging (Everything at Debug Level)
                let allLoggingItem = createMenuItem(
                    title: "All Logging (Everything)",
                    action: #selector(enableAllLogging),
                    sfSymbolName: "list.bullet.clipboard")

                // Check if current settings match all logging preset
                if currentLevel == .debug
                    && LogCategory.allCases.allSatisfy({ loggingService.isCategoryEnabled($0) })
                {
                    allLoggingItem.state = .on
                }
                mainLoggingSubmenu.addItem(allLoggingItem)
                mainLoggingSubmenu.addItem(NSMenuItem.separator())

                // Feature-specific debugging options
                let featureSubmenu = NSMenu()

                // Window Management Debugging
                let subWindowManagementItem = createMenuItem(
                    title: "Window Management Debugging",
                    action: #selector(enableWindowSnappingLogging),
                    sfSymbolName: "inset.filled.leadinghalf.toptrailing.bottomtrailing.rectangle")

                // Check if current settings match window management debugging preset
                if currentLevel == .debug && loggingService.isCategoryEnabled(.windowSnapping)
                    && loggingService.isCategoryEnabled(.windowPositioning)
                    && loggingService.isCategoryEnabled(.windowCalculation)
                    && !loggingService.isCategoryEnabled(.screenDetection)
                    && !loggingService.isCategoryEnabled(.screenDebug)
                {
                    subWindowManagementItem.state = .on
                }
                featureSubmenu.addItem(subWindowManagementItem)

                // Screen Detection Debugging
                let subScreenDetectionItem = createMenuItem(
                    title: "Screen Detection Debugging",
                    action: #selector(enableScreenDetectionLogging),
                    sfSymbolName: "display")

                // Check if current settings match screen detection debugging preset
                if currentLevel == .debug && loggingService.isCategoryEnabled(.screenDetection)
                    && loggingService.isCategoryEnabled(.screenDebug)
                    && !loggingService.isCategoryEnabled(.windowSnapping)
                    && !loggingService.isCategoryEnabled(.windowPositioning)
                    && !loggingService.isCategoryEnabled(.windowCalculation)
                {
                    subScreenDetectionItem.state = .on
                }
                featureSubmenu.addItem(subScreenDetectionItem)

                // Workspaces Debugging
                let workspacesItem = createMenuItem(
                    title: "Workspaces Debugging",
                    action: #selector(enableWorkspacesLogging),
                    sfSymbolName: "rectangle.on.rectangle")

                // Check if current settings match workspaces debugging preset
                if currentLevel == .debug && loggingService.isCategoryEnabled(.workspaces)
                    && !loggingService.isCategoryEnabled(.windowSnapping)
                    && !loggingService.isCategoryEnabled(.screenDetection)
                {
                    workspacesItem.state = .on
                }
                featureSubmenu.addItem(workspacesItem)

                // Workspace Preview Debugging (feature removed)
                let workspacePreviewItem = createMenuItem(
                    title: "Workspace Preview Debugging (Removed)",
                    action: #selector(enableWorkspacePreviewLogging),
                    sfSymbolName: "rectangle.grid.2x2.fill")

                // Always set to off since the feature is removed
                workspacePreviewItem.state = .off
                featureSubmenu.addItem(workspacePreviewItem)

                // User Interface Debugging
                let uiItem = createMenuItem(
                    title: "User Interface Debugging",
                    action: #selector(enableUserInterfaceLogging),
                    sfSymbolName: "slider.horizontal.below.rectangle")

                // Check if current settings match UI debugging preset
                if currentLevel == .debug && loggingService.isCategoryEnabled(.userInterface)
                    && loggingService.isCategoryEnabled(.menuBar)
                    && loggingService.isCategoryEnabled(.toast)
                    && !loggingService.isCategoryEnabled(.windowSnapping)
                    && !loggingService.isCategoryEnabled(.screenDetection)
                {
                    uiItem.state = .on
                }
                featureSubmenu.addItem(uiItem)

                // Shortcuts Debugging
                let shortcutsItem = createMenuItem(
                    title: "Shortcuts Debugging",
                    action: #selector(enableShortcutsLogging),
                    sfSymbolName: "keyboard")

                // Check if current settings match shortcuts debugging preset
                if currentLevel == .debug && loggingService.isCategoryEnabled(.shortcuts)
                    && loggingService.isCategoryEnabled(.dragToSnap)
                    && !loggingService.isCategoryEnabled(.windowSnapping)
                    && !loggingService.isCategoryEnabled(.screenDetection)
                {
                    shortcutsItem.state = .on
                }
                featureSubmenu.addItem(shortcutsItem)

                // System Debugging
                let systemItem = createMenuItem(
                    title: "System Debugging",
                    action: #selector(enableSystemLogging),
                    sfSymbolName: "gear")

                // Check if current settings match system debugging preset
                if currentLevel == .debug && loggingService.isCategoryEnabled(.system)
                    && loggingService.isCategoryEnabled(.startup)
                    && loggingService.isCategoryEnabled(.permissions)
                    && loggingService.isCategoryEnabled(.accessibility)
                    && !loggingService.isCategoryEnabled(.windowSnapping)
                    && !loggingService.isCategoryEnabled(.screenDetection)
                {
                    systemItem.state = .on
                }
                featureSubmenu.addItem(systemItem)

                // Add feature submenu to main logging menu
                let featureItem = createMenuItem(
                    title: "Feature Debugging",
                    action: nil,
                    sfSymbolName: "rectangle.3.group")
                featureItem.submenu = featureSubmenu
                mainLoggingSubmenu.addItem(featureItem)

                mainLoggingSubmenu.addItem(NSMenuItem.separator())

                // Log level submenu
                let logLevelSubmenu = NSMenu()
                for level in LogLevel.allCases {
                    let item = createMenuItem(
                        title: level.rawValue.capitalized,
                        action: #selector(setLogLevel(_:)))
                    item.representedObject = level

                    // Check mark for current level
                    if level == LoggingService.shared.minimumLogLevel {
                        item.state = .on
                    }

                    logLevelSubmenu.addItem(item)
                }

                let logLevelItem = createMenuItem(
                    title: "Minimum Log Level",
                    action: nil,
                    sfSymbolName: "slider.horizontal.3")
                logLevelItem.submenu = logLevelSubmenu
                mainLoggingSubmenu.addItem(logLevelItem)

                // Add Show Log Viewer item
                mainLoggingSubmenu.addItem(NSMenuItem.separator())
                mainLoggingSubmenu.addItem(
                    createMenuItem(
                        title: "Show Log Viewer",
                        action: #selector(showLogViewer),
                        sfSymbolName: "list.bullet.rectangle"
                    )
                )

                // Add the main logging submenu to the main menu
                menu.addItem(NSMenuItem.separator())
                let mainLoggingItem = createMenuItem(
                    title: "Logging",
                    action: nil,
                    sfSymbolName: "text.append")
                mainLoggingItem.submenu = mainLoggingSubmenu
                menu.addItem(mainLoggingItem)
            #endif

            menu.addItem(NSMenuItem.separator())

            menu.addItem(
                createMenuItem(
                    title: "About Snapback",
                    action: #selector(openAbout),
                    sfSymbolName: "info.circle"
                )
            )

            menu.addItem(
                createMenuItem(
                    title: "Settings...", action: #selector(openSettings), keyEquivalent: "",
                    sfSymbolName: "gearshape"))

            menu.addItem(
                createMenuItem(
                    title: "Help", action: #selector(openHelp), keyEquivalent: "",
                    sfSymbolName: "questionmark.circle"))

            menu.addItem(NSMenuItem.separator())
            menu.addItem(
                createMenuItem(
                    title: "Quit Snapback", action: #selector(NSApplication.terminate(_:)),
                    keyEquivalent: "", sfSymbolName: "power"))
        }

        return menu
    }

    // MARK: - Menu Action Handlers
    @objc func snapLeft() { windowSnappingService.snapFrontmostWindow(to: .leftHalf) }
    @objc func snapRight() { windowSnappingService.snapFrontmostWindow(to: .rightHalf) }
    @objc func snapTopHalf() { windowSnappingService.snapFrontmostWindow(to: .topHalf) }
    @objc func snapBottomHalf() { windowSnappingService.snapFrontmostWindow(to: .bottomHalf) }
    @objc func snapFullscreen() { windowSnappingService.snapFrontmostWindow(to: .fullscreen) }
    @objc func snapLeftThird() { windowSnappingService.snapFrontmostWindow(to: .leftThird) }
    @objc func snapCenterThird() { windowSnappingService.snapFrontmostWindow(to: .centerThird) }
    @objc func snapRightThird() { windowSnappingService.snapFrontmostWindow(to: .rightThird) }
    @objc func snapTopLeftQuarter() {
        windowSnappingService.snapFrontmostWindow(to: .topLeftQuarter)
    }
    @objc func snapTopRightQuarter() {
        windowSnappingService.snapFrontmostWindow(to: .topRightQuarter)
    }
    @objc func snapBottomLeftQuarter() {
        windowSnappingService.snapFrontmostWindow(to: .bottomLeftQuarter)
    }
    @objc func snapBottomRightQuarter() {
        windowSnappingService.snapFrontmostWindow(to: .bottomRightQuarter)
    }
    @objc func snapLeftTwoThirds() { windowSnappingService.snapFrontmostWindow(to: .leftTwoThirds) }
    @objc func snapCenterTwoThirds() {
        windowSnappingService.snapFrontmostWindow(to: .centerTwoThirds)
    }
    @objc func snapRightTwoThirds() {
        windowSnappingService.snapFrontmostWindow(to: .rightTwoThirds)
    }

    @objc func openWorkspaceManager() { windowManager.openWorkspaceManager() }
    @objc func saveCurrentWorkspace() {
        print("🔥 APPDELEGATE: saveCurrentWorkspace called!")

        logger.info(
            "saveCurrentWorkspace called",
            service: serviceName,
            category: .userInterface
        )

        // Get window positions with normalized coordinates
        print("🔥 APPDELEGATE: Getting window infos...")
        let windowInfos = WindowCaptureService.getCurrentAppsAndWindowPositionsNormalized()
        print("🔥 APPDELEGATE: Captured \(windowInfos.count) window infos")

        logger.debug(
            "Captured \(windowInfos.count) window infos",
            service: serviceName,
            category: .userInterface
        )

        print("🔥 APPDELEGATE: Calling windowManager.showSaveWorkspacePopup...")
        logger.debug(
            "Calling windowManager.showSaveWorkspacePopup",
            service: serviceName,
            category: .userInterface
        )
        windowManager.showSaveWorkspacePopup(windowInfos: windowInfos)

        print("🔥 APPDELEGATE: windowManager.showSaveWorkspacePopup call completed")
        logger.debug(
            "windowManager.showSaveWorkspacePopup call completed",
            service: serviceName,
            category: .userInterface
        )
    }
    @objc func restoreSavedWorkspaceMenuItem(_ sender: NSMenuItem) {
        guard let workspace = sender.representedObject as? Workspace else { return }
        workspaceService.triggerRestoreWorkspace(workspace: workspace)
    }
    @objc func applySavedCustomLayoutMenuItem(_ sender: NSMenuItem) {
        guard let workspace = sender.representedObject as? Workspace else { return }
        workspaceService.triggerApplyCustomLayout(workspace: workspace)
    }
    @objc func openSettings() { windowManager.openSettings() }
    @objc func openAbout() { windowManager.openAbout() }
    @objc func openHelp() {
        logger.info("Opening help website", service: serviceName)
        if let url = URL(string: "https://snapbackp.com") {
            NSWorkspace.shared.open(url)
            logger.debug(
                "Successfully opened help URL: https://snapbackp.com", service: serviceName)
        } else {
            logger.error("Failed to create URL for help website", service: serviceName)
        }
    }

    // MARK: - Conflict Detection

    /// Check for other window management applications that might conflict with Snapback
    private func checkForConflictingApps() {
        let (hasConflict, conflictingAppName) = ConflictingAppsChecker.shared
            .checkForConflictingApps()

        if hasConflict, let appName = conflictingAppName {
            logger.warning("Detected conflicting window manager: \(appName)", service: serviceName)

            // Show alert to user
            let alert = NSAlert()
            alert.messageText = "Potential window manager conflict: \(appName)"
            alert.informativeText =
                "Since \(appName) might have some overlapping behavior with Snapback, it's recommended that you either disable or quit \(appName)."
            alert.alertStyle = .warning
            alert.addButton(withTitle: "OK")

            // Show the alert
            DispatchQueue.main.async {
                alert.runModal()
            }
        }
    }

    /// Check for applications known to have issues with drag-to-snap
    private func checkForProblematicApps() {
        // Only check if drag-to-snap is enabled
        guard UserDefaults.standard.bool(forKey: "dragToSnapEnabled") else { return }

        // Only check if we haven't already notified the user
        guard !UserDefaults.standard.bool(forKey: "notifiedOfProblemApps") else { return }

        let (hasProblematicApp, problematicAppName) = ConflictingAppsChecker.shared
            .checkForProblematicApps()

        if hasProblematicApp, let appName = problematicAppName {
            logger.warning(
                "Detected problematic app for drag-to-snap: \(appName)", service: serviceName)

            // Show alert to user
            let alert = NSAlert()
            alert.messageText = "Potential issue with \(appName)"
            alert.informativeText =
                "Snapback's drag-to-snap feature may not work correctly with \(appName). You can disable drag-to-snap in Settings if you experience issues."
            alert.alertStyle = .informational
            alert.addButton(withTitle: "OK")
            alert.addButton(withTitle: "Don't Show Again")

            // Show the alert
            DispatchQueue.main.async {
                let response = alert.runModal()

                // If user clicked "Don't Show Again"
                if response == NSApplication.ModalResponse.alertSecondButtonReturn {
                    UserDefaults.standard.set(true, forKey: "notifiedOfProblemApps")
                }
            }
        }
    }

    @objc func toggleDragToSnap() {
        let currentValue = UserDefaults.standard.bool(forKey: "dragToSnapEnabled")
        let newValue = !currentValue
        UserDefaults.standard.set(newValue, forKey: "dragToSnapEnabled")

        // Use Task to call the MainActor-isolated method
        Task { @MainActor in
            snappingManager.setEnabled(newValue)
        }

        setupMenu()  // Refresh menu to update checkbox state
    }

    // MARK: - Logging Menu Actions

    @objc func enableWindowSnappingLogging() {
        // Use direct print for debugging
        #if DEBUG
            print("🔴 enableWindowSnappingLogging method called")
        #endif
        logger.debug("enableWindowSnappingLogging called", service: serviceName)
        LoggingManager.shared.enableWindowManagementDebugging()
        // Menu will be updated by the notification observer
    }

    @objc func enableScreenDetectionLogging() {
        // Use direct print for debugging
        #if DEBUG
            print("🔴 enableScreenDetectionLogging method called")
        #endif
        logger.debug("enableScreenDetectionLogging called", service: serviceName)
        LoggingManager.shared.enableScreenDetectionDebugging()
        // Menu will be updated by the notification observer
    }

    @objc func enableWorkspacesLogging() {
        // Use direct print for debugging
        #if DEBUG
            print("🔴 enableWorkspacesLogging method called")
        #endif
        logger.debug("enableWorkspacesLogging called", service: serviceName)
        LoggingManager.shared.enableWorkspacesDebugging()
        // Menu will be updated by the notification observer
    }

    @objc func enableWorkspacePreviewLogging() {
        // Use direct print for debugging
        #if DEBUG
            print("🔴 enableWorkspacePreviewLogging method called")
        #endif
        logger.debug(
            "enableWorkspacePreviewLogging called (method will be removed)", service: serviceName)
        // Workspace preview logging removed
        // Menu will be updated by the notification observer
    }

    @objc func enableUserInterfaceLogging() {
        // Use direct print for debugging
        #if DEBUG
            print("🔴 enableUserInterfaceLogging method called")
        #endif
        logger.debug("enableUserInterfaceLogging called", service: serviceName)
        LoggingManager.shared.enableUserInterfaceDebugging()
        // Menu will be updated by the notification observer
    }

    @objc func enableShortcutsLogging() {
        // Use direct print for debugging
        #if DEBUG
            print("🔴 enableShortcutsLogging method called")
        #endif
        logger.debug("enableShortcutsLogging called", service: serviceName)
        LoggingManager.shared.enableShortcutsDebugging()
        // Menu will be updated by the notification observer
    }

    @objc func enableSystemLogging() {
        // Use direct print for debugging
        #if DEBUG
            print("🔴 enableSystemLogging method called")
        #endif
        logger.debug("enableSystemLogging called", service: serviceName)
        LoggingManager.shared.enableSystemDebugging()
        // Menu will be updated by the notification observer
    }

    @objc func enableGeneralLogging() {
        // Use direct print for debugging
        #if DEBUG
            print("🔴 enableGeneralLogging method called")
        #endif
        logger.debug("enableGeneralLogging called", service: serviceName)
        LoggingManager.shared.enableGeneralUsageLogging()
        // Menu will be updated by the notification observer
    }

    @objc func enableWarningsAndErrorsOnly() {
        // Use direct print for debugging
        #if DEBUG
            print("🔴 enableWarningsAndErrorsOnly method called")
        #endif
        logger.debug("enableWarningsAndErrorsOnly called", service: serviceName)
        LoggingManager.shared.disableDebugLogs()
        // Menu will be updated by the notification observer
    }

    @objc func enableAllLogging() {
        // Use direct print for debugging
        #if DEBUG
            print("🔴 enableAllLogging method called")
        #endif
        logger.debug("enableAllLogging called", service: serviceName)
        LoggingManager.shared.enableAllLogging()
        // Menu will be updated by the notification observer
    }

    @objc func setLogLevel(_ sender: NSMenuItem) {
        // Use direct print for debugging
        #if DEBUG
            print("🔴 setLogLevel method called")
        #endif
        if let level = sender.representedObject as? LogLevel {
            #if DEBUG
                print("🔴 setLogLevel called with level: \(level.rawValue)")
            #endif
            logger.debug("setLogLevel called with level: \(level.rawValue)", service: serviceName)
            LoggingManager.shared.setMinimumLogLevel(level)
            // Menu will be updated by the notification observer
        }
    }

    @objc func showLogViewer() {
        logger.info("Opening log viewer", service: serviceName)
        LogViewerWindowController.shared.showWindow()
    }

    // MARK: - Drag-to-Snap Notification Handlers

    @objc func handleDragToSnapEnabledChanged(_ notification: Notification) {
        if let enabled = notification.object as? Bool {
            logger.info("Drag-to-snap enabled changed to: \(enabled)", service: serviceName)
            Task { @MainActor in
                snappingManager.setEnabled(enabled)
            }
            setupMenu()  // Refresh menu to update checkbox state
        }
    }

    @objc func handleSnapModifiersChanged(_ notification: Notification) {
        if let modifierValue = notification.object as? UInt {
            logger.info("Snap modifiers changed to: \(modifierValue)", service: serviceName)
            Task { @MainActor in
                snappingManager.setSnapModifiers(NSEvent.ModifierFlags(rawValue: modifierValue))
            }
        }
    }

    @objc func handleAvoidSystemConflictsChanged(_ notification: Notification) {
        if let avoid = notification.object as? Bool {
            logger.info("Avoid system conflicts changed to: \(avoid)", service: serviceName)
            Task { @MainActor in
                snappingManager.setAvoidSystemConflicts(avoid)
            }
        }
    }

    // MARK: - Permission Handling

    /// Check if accessibility permissions are granted and request if needed
    func checkAndRequestPermissions() {
        // Check if accessibility permissions are granted
        isAccessibilityPermissionGranted = PermissionManager.shared.checkAccessibilityPermission()

        // Update menu visibility based on permission status
        updateMenuVisibility()

        // If permissions are not granted, show alert
        if !isAccessibilityPermissionGranted {
            PermissionManager.shared.requestAccessibilityPermission()
        }
    }

    /// Handle permission status changes
    @objc func handlePermissionStatusChanged(_ notification: Notification) {
        if let granted = notification.object as? Bool {
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }

                // Update our local state
                self.isAccessibilityPermissionGranted = granted

                // Update menu visibility
                self.updateMenuVisibility()

                // Log the change
                self.logger.info(
                    "Accessibility permission status changed to: \(granted)",
                    service: self.serviceName)
            }
        }
    }

    /// Handle explicit requests to refresh the status menu
    @objc func handleRefreshStatusMenu(_ notification: Notification) {
        print("🔄 REFRESH DEBUG: Received notification to refresh status menu")
        logger.debug(
            "Received notification to refresh status menu",
            service: serviceName,
            category: .userInterface
        )

        DispatchQueue.main.async { [weak self] in
            guard let self = self else {
                print("⚠️ REFRESH DEBUG: Self is nil, cannot refresh menu")
                return
            }

            // Log the refresh request
            print("🔄 REFRESH DEBUG: On main thread, preparing to rebuild menu")
            self.logger.info(
                "Explicit menu refresh requested, rebuilding menu",
                service: self.serviceName)

            // Synchronize KeyboardShortcuts with workspace model
            print("🔄 REFRESH DEBUG: Synchronizing KeyboardShortcuts with workspace model")
            self.workspaceService.synchronizeKeyboardShortcuts()
            print("✅ REFRESH DEBUG: KeyboardShortcuts synchronized")

            // Rebuild the menu
            print("🔄 REFRESH DEBUG: Calling setupMenu()")
            self.setupMenu()
            print("✅ REFRESH DEBUG: Menu rebuild completed")
        }
    }

    /// Update menu visibility based on permission status
    private func updateMenuVisibility() {
        if isAccessibilityPermissionGranted {
            // If permissions are granted, show the menu
            setupMenu()
        } else {
            // If permissions are not granted, show a limited menu
            setupLimitedMenu()
        }
    }

    /// Set up a limited menu when permissions are not granted
    private func setupLimitedMenu() {
        guard let statusBarItem = statusBarItem else { return }

        let menu = NSMenu()

        // Add a menu item to request permissions
        let permissionItem = NSMenuItem(
            title: "Grant Accessibility Permissions",
            action: #selector(requestPermissions),
            keyEquivalent: ""
        )
        permissionItem.target = self
        menu.addItem(permissionItem)

        // Add About and Quit items
        menu.addItem(NSMenuItem.separator())

        // About menu item
        let aboutItem = NSMenuItem(
            title: "About Snapback",
            action: #selector(openAbout),
            keyEquivalent: ""
        )
        aboutItem.target = self
        if let image = NSImage(systemSymbolName: "info.circle", accessibilityDescription: "About") {
            image.isTemplate = true
            aboutItem.image = image
        }
        menu.addItem(aboutItem)

        // Help menu item
        let helpItem = NSMenuItem(
            title: "Help",
            action: #selector(openHelp),
            keyEquivalent: ""
        )
        helpItem.target = self
        if let image = NSImage(
            systemSymbolName: "questionmark.circle", accessibilityDescription: "Help")
        {
            image.isTemplate = true
            helpItem.image = image
        }
        menu.addItem(helpItem)

        menu.addItem(NSMenuItem.separator())

        // Quit menu item
        let quitItem = NSMenuItem(
            title: "Quit Snapback",
            action: #selector(NSApplication.terminate(_:)),
            keyEquivalent: "q"
        )
        quitItem.target = NSApp
        if let image = NSImage(systemSymbolName: "power", accessibilityDescription: "Quit") {
            image.isTemplate = true
            quitItem.image = image
        }
        menu.addItem(quitItem)

        statusBarItem.menu = menu
    }

    /// Request permissions manually
    @objc func requestPermissions() {
        PermissionManager.shared.requestAccessibilityPermission()
    }

    // MARK: - Toast System

    /// Initialize the toast notification system
    private func initializeToastSystem() {
        // Initialize the toast window controller
        _ = ToastWindowController.shared

        // Show a welcome toast when permissions are granted
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(showWelcomeToast),
            name: PermissionManager.permissionStatusChanged,
            object: true
        )
    }

    /// Show a welcome toast when permissions are granted
    @objc private func showWelcomeToast() {
        ToastManager.shared.showSuccess(
            title: "Snapback is Ready",
            message: "Accessibility permissions granted. You can now use all features.",
            duration: 3.0
        )
    }

    /// Handle window management enabled/disabled changes
    @objc func handleWindowManagementEnabledChanged(_ notification: Notification) {
        let isEnabled = notification.object as? Bool ?? true

        logger.info(
            "Window management features \(isEnabled ? "enabled" : "disabled"), updating shortcuts",
            service: serviceName,
            category: .general
        )

        // Update shortcuts registration
        if isEnabled {
            shortcutService.registerWindowManagementShortcuts()
        } else {
            shortcutService.unregisterWindowManagementShortcuts()
        }

        // Menu will be refreshed by the RefreshStatusMenu notification that was also posted
    }

    // MARK: - Helper Functions

    // Helper method to determine if screens are arranged vertically
    private func isVerticalArrangement(screens: [NSScreen]) -> Bool {
        guard screens.count > 1 else { return false }

        // Sort screens by Y position (bottom to top)
        let sortedScreens = screens.sorted(by: { $0.frame.minY < $1.frame.minY })

        // Check if screens are stacked vertically
        for i in 0..<(sortedScreens.count - 1) {
            let currentScreen = sortedScreens[i]
            let nextScreen = sortedScreens[i + 1]

            // Calculate horizontal overlap
            let horizontalOverlap =
                min(currentScreen.frame.maxX, nextScreen.frame.maxX)
                - max(currentScreen.frame.minX, nextScreen.frame.minX)

            // If there's significant horizontal overlap, screens are arranged vertically
            if horizontalOverlap > 0 {
                return true
            }
        }

        return false
    }

    /// Convert a key code to a string representation for menu shortcuts
    private func keycodeToString(_ keyCode: UInt16) -> String? {
        logger.debug(
            "keycodeToString called with keyCode=\(keyCode) (0x\(String(keyCode, radix: 16)))",
            service: serviceName,
            category: .shortcuts
        )

        // Common key codes
        let keyCodeMap: [UInt16: String] = [
            0x00: "a", 0x01: "s", 0x02: "d", 0x03: "f", 0x04: "h", 0x05: "g", 0x06: "z", 0x07: "x",
            0x08: "c", 0x09: "v", 0x0B: "b", 0x0C: "q", 0x0D: "w", 0x0E: "e", 0x0F: "r",
            0x10: "y", 0x11: "t", 0x12: "1", 0x13: "2", 0x14: "3", 0x15: "4", 0x16: "6", 0x17: "5",
            0x18: "=", 0x19: "9", 0x1A: "7", 0x1B: "-", 0x1C: "8", 0x1D: "0", 0x1E: "]", 0x1F: "o",
            0x20: "u", 0x21: "[", 0x22: "i", 0x23: "p", 0x25: "l", 0x26: "j", 0x27: "'", 0x28: "k",
            0x29: ";", 0x2A: "\\", 0x2B: ",", 0x2C: "/", 0x2D: "n", 0x2E: "m", 0x2F: ".",
            0x24: "\r", 0x30: "\t", 0x31: " ", 0x33: "\u{8}",  // Delete
            0x35: "\u{1B}",  // Escape
            0x7E: "↑", 0x7D: "↓", 0x7B: "←", 0x7C: "→",  // Arrow keys

            // Additional keys for better compatibility
            0x41: ".", 0x43: "*", 0x45: "+", 0x4B: "/", 0x4C: "\r", 0x51: "=", 0x52: "0", 0x53: "1",
            0x54: "2", 0x55: "3", 0x56: "4", 0x57: "5", 0x58: "6", 0x59: "7", 0x5B: "8", 0x5C: "9",

            // Function keys - these won't work as menu shortcuts but we'll map them anyway
            0x7A: "f1", 0x78: "f2", 0x63: "f3", 0x76: "f4", 0x60: "f5", 0x61: "f6",
            0x62: "f7", 0x64: "f8", 0x65: "f9", 0x6D: "f10", 0x67: "f11", 0x6F: "f12",
            0x69: "f13", 0x6B: "f14", 0x71: "f15", 0x6A: "f16", 0x40: "f17", 0x4F: "f18",
            0x50: "f19",
        ]

        // Log all key codes for debugging
        logger.debug(
            "Available key codes in map: \(keyCodeMap.keys.sorted().map { "0x\(String($0, radix: 16))" }.joined(separator: ", "))",
            service: serviceName,
            category: .shortcuts
        )

        // Check if the key code is in the map
        if keyCodeMap.keys.contains(keyCode) {
            logger.debug(
                "Key code found in map",
                service: serviceName,
                category: .shortcuts
            )
        } else {
            logger.warning(
                "Key code NOT found in map",
                service: serviceName,
                category: .shortcuts
            )
        }

        let result = keyCodeMap[keyCode]
        logger.debug(
            "keycodeToString: keyCode=\(keyCode) (0x\(String(keyCode, radix: 16))) -> \(result ?? "nil")",
            service: serviceName)

        // Log the result
        if let result = result {
            logger.debug(
                "Converted to '\(result)'",
                service: serviceName,
                category: .shortcuts
            )
        } else {
            logger.warning(
                "Failed to convert keyCode \(keyCode) to string",
                service: serviceName,
                category: .shortcuts
            )
        }

        return result
    }
}
