import AppKit
import ApplicationServices  // For AXIsProcessTrusted
import Foundation

/// Manages accessibility permissions for the app
class PermissionManager: ObservableObject {
    static let shared = PermissionManager()

    /// Published property to track if accessibility permissions are granted
    @Published var isAccessibilityPermissionGranted: Bool = false

    /// Timer to periodically check permissions
    private var permissionCheckTimer: Timer?

    /// Notification name for permission changes
    static let permissionStatusChanged = Notification.Name("PermissionStatusChanged")

    /// Track if we've already shown a permission request this session
    private var hasShownPermissionRequest: Bool = false

    /// Logger for debugging
    private let logger = LoggingService.shared
    private let serviceName = "PermissionManager"

    private init() {
        // Check initial permission status
        isAccessibilityPermissionGranted = checkAccessibilityPermission()

        logger.info(
            "PermissionManager initialized - Accessibility permission granted: \(isAccessibilityPermissionGranted)",
            service: serviceName,
            category: .permissions
        )

        // Set up timer to check permissions periodically with longer interval for development
        let checkInterval: TimeInterval = isDevelopmentEnvironment() ? 5.0 : 2.0
        permissionCheckTimer = Timer.scheduledTimer(
            timeInterval: checkInterval,
            target: self,
            selector: #selector(checkPermissionStatus),
            userInfo: nil,
            repeats: true
        )

        logger.debug(
            "Permission check timer started with interval: \(checkInterval)s",
            service: serviceName,
            category: .permissions
        )
    }

    deinit {
        permissionCheckTimer?.invalidate()
        logger.debug(
            "PermissionManager deinitialized", service: serviceName, category: .permissions)
    }

    /// Detect if we're running in a development environment
    private func isDevelopmentEnvironment() -> Bool {
        #if DEBUG
            return true
        #else
            // Additional checks for development environment
            let isXcodeAttached =
                ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] != nil
                || ProcessInfo.processInfo.environment["__XCODE_BUILT_PRODUCTS_DIR_PATHS"] != nil

            // Check if bundle is in a development location
            let bundlePath = Bundle.main.bundlePath
            let isDevelopmentPath =
                bundlePath.contains("/DerivedData/") || bundlePath.contains("/Build/Products/")
                || bundlePath.contains("/Debug/")

            logger.debug(
                "Development environment check - Xcode: \(isXcodeAttached), Path: \(isDevelopmentPath), Bundle: \(bundlePath)",
                service: serviceName,
                category: .permissions
            )

            return isXcodeAttached || isDevelopmentPath
        #endif
    }

    /// Check if accessibility permissions are granted
    func checkAccessibilityPermission() -> Bool {
        return AXIsProcessTrusted()
    }

    /// Request accessibility permissions with development environment awareness
    func requestAccessibilityPermission() {
        // Prevent multiple permission requests in the same session
        guard !hasShownPermissionRequest else {
            logger.debug(
                "Permission request already shown this session, skipping",
                service: serviceName,
                category: .permissions
            )
            return
        }

        // In development environment, be less aggressive about showing permission dialogs
        if isDevelopmentEnvironment() {
            logger.info(
                "Development environment detected - showing less intrusive permission request",
                service: serviceName,
                category: .permissions
            )

            // Mark that we've shown the request to prevent repeated prompts
            hasShownPermissionRequest = true

            // Show a less intrusive notification instead of a modal dialog
            showDevelopmentPermissionNotification()
            return
        }

        // Production environment - show full permission dialog
        hasShownPermissionRequest = true
        showProductionPermissionDialog()
    }

    /// Show a less intrusive permission notification for development
    private func showDevelopmentPermissionNotification() {
        logger.info(
            "Accessibility permissions needed for full functionality. Grant in System Settings > Privacy & Security > Accessibility if needed.",
            service: serviceName,
            category: .permissions
        )

        // Optionally show a toast notification instead of a modal dialog
        DispatchQueue.main.async {
            // You can implement a toast notification here if desired
            // For now, just log the requirement
        }
    }

    /// Show the full permission dialog for production
    private func showProductionPermissionDialog() {
        let alert = NSAlert()
        alert.messageText = "Accessibility Permissions Required"
        alert.informativeText =
            "Snapback needs Accessibility permissions to manage window positions. Please grant access in System Settings > Privacy & Security > Accessibility."
        alert.alertStyle = .warning
        alert.addButton(withTitle: "Open Settings")
        alert.addButton(withTitle: "Cancel")

        if alert.runModal() == .alertFirstButtonReturn {
            if let url = URL(
                string:
                    "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")
            {
                NSWorkspace.shared.open(url)
            }
        }
    }

    /// Periodically check permission status
    @objc private func checkPermissionStatus() {
        let currentStatus = checkAccessibilityPermission()

        // If permission status has changed, update and notify
        if currentStatus != isAccessibilityPermissionGranted {
            let previousStatus = isAccessibilityPermissionGranted
            isAccessibilityPermissionGranted = currentStatus

            logger.info(
                "Accessibility permission status changed: \(previousStatus) -> \(currentStatus)",
                service: serviceName,
                category: .permissions
            )

            // Post notification about permission change
            NotificationCenter.default.post(
                name: PermissionManager.permissionStatusChanged,
                object: isAccessibilityPermissionGranted
            )

            // Reset the permission request flag when permissions are granted
            if currentStatus {
                hasShownPermissionRequest = false
                logger.info(
                    "Accessibility permissions granted - resetting request flag",
                    service: serviceName,
                    category: .permissions
                )
            }
        }
    }

    /// Reset the permission request flag (useful for testing or manual reset)
    func resetPermissionRequestFlag() {
        hasShownPermissionRequest = false
        logger.debug(
            "Permission request flag manually reset",
            service: serviceName,
            category: .permissions
        )
    }
}
