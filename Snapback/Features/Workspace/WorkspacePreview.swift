import AppKit
import SwiftUI

/// A view that displays a preview of a workspace with multiple displays and windows
struct WorkspacePreview: View {
    // MARK: - Properties

    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "WorkspacePreview"

    // Window data
    let windowInfos: [WindowInfo]

    // Callback for when a window is closed
    var onWindowClose: ((WindowInfo) -> Void)?

    // Sizing
    let previewSize: CGSize
    let padding: CGFloat = 10

    // State
    @State private var coordinateSystem: CoordinateSystem?

    // MARK: - Initialization

    init(
        windowInfos: [WindowInfo], previewSize: CGSize = CGSize(width: 500, height: 220),
        onWindowClose: ((WindowInfo) -> Void)? = nil
    ) {
        self.windowInfos = windowInfos
        self.previewSize = previewSize
        self.onWindowClose = onWindowClose
    }

    // MARK: - Body

    var body: some View {
        ZStack(alignment: .center) {
            if let coordSystem = coordinateSystem {
                // Preview content - centered in the view
                ZStack {
                    // Displays
                    ForEach(coordSystem.displays, id: \.id) { display in
                        DisplayView(
                            display: display,
                            coordinateSystem: coordSystem,
                            padding: padding,
                            previewSize: previewSize
                        )
                    }

                    // Windows
                    ForEach(windowInfos.indices, id: \.self) { index in
                        WindowPreviewItem(
                            windowInfo: windowInfos[index],
                            index: index,
                            coordinateSystem: coordSystem,
                            padding: padding,
                            totalCount: windowInfos.count,
                            previewSize: previewSize,
                            onClose: { windowInfo in
                                logger.debug(
                                    "Window close requested for \(windowInfo.appBundleIdentifier ?? "Unknown")",
                                    service: serviceName)
                                onWindowClose?(windowInfo)
                            }
                        )
                        // Add a unique ID based on the app bundle ID to ensure proper view recreation
                        .id(windowInfos[index].appBundleIdentifier ?? UUID().uuidString)
                    }
                }
                // Use center alignment instead of frame and padding
                .frame(width: previewSize.width, height: previewSize.height)

                // Overlay display tags so they are always visible
                // Always check for vertical stacking regardless of overall arrangement classification
                let shouldCheckVerticalStacking = true

                ForEach(coordSystem.displays, id: \.id) { display in
                    let previewFrame = coordSystem.convertToPreview(
                        display.frame, previewSize: previewSize, padding: padding, isDisplay: true)

                    // For vertical/mixed arrangements, determine tag position based on display position
                    // Use the same horizontal overlap logic as the existing codebase
                    let isBottomDisplay =
                        shouldCheckVerticalStacking
                        && coordSystem.displays.contains { otherDisplay in
                            otherDisplay.id != display.id
                                && otherDisplay.frame.minY < display.frame.minY  // Other display is above this one (lower Y value)
                                && {
                                    // Use the same horizontal overlap calculation as existing code
                                    let horizontalOverlap =
                                        min(display.frame.maxX, otherDisplay.frame.maxX)
                                        - max(display.frame.minX, otherDisplay.frame.minX)
                                    let minWidth = min(
                                        display.frame.width, otherDisplay.frame.width)
                                    let horizontalOverlapPercentage = horizontalOverlap / minWidth
                                    return horizontalOverlapPercentage > 0.5  // Significant horizontal overlap
                                }()
                        }
                    // Bottom displays show tag below, top displays show tag above
                    let shouldShowBelow = isBottomDisplay

                    HStack(spacing: 4) {
                        Image(systemName: "display")
                            .font(.system(size: 10))
                        Text(getDisplayLabel(for: display))
                            .font(.system(size: 10, weight: display.isMain ? .bold : .regular))
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(display.isMain ? Color.blue.opacity(0.7) : Color.gray.opacity(0.7))
                    .foregroundColor(.white)
                    .cornerRadius(6)
                    .shadow(radius: 2)
                    .position(
                        x: previewFrame.midX,
                        y: shouldShowBelow
                            ? min(previewFrame.maxY + 16, previewSize.height - 12)
                            : max(previewFrame.minY - 16, 12)
                    )
                }
            }
        }
        .frame(width: previewSize.width, height: previewSize.height)
        .onAppear {
            // Setup displays when the view appears
            setupDisplays()

            // Log that the preview is ready
            if let coordSystem = coordinateSystem {
                logger.info(
                    "Workspace preview ready with \(windowInfos.count) windows across \(coordSystem.displays.count) displays",
                    service: serviceName
                )
            }
        }
        // Add a notification observer to refresh the display information when the screen configuration changes
        .onReceive(
            NotificationCenter.default.publisher(
                for: NSApplication.didChangeScreenParametersNotification)
        ) { _ in
            logger.info(
                "Screen parameters changed - refreshing display information",
                service: serviceName
            )
            // Refresh the display information
            setupDisplays()
        }
    }

    // MARK: - Enhanced Coordinate System Setup

    // MARK: - Setup

    private func setupDisplays() {
        // Log the start of display setup
        logger.info(
            "┌─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )
        logger.info(
            "│ WORKSPACE PREVIEW - SETUP DISPLAYS (Enhanced System)",
            service: serviceName
        )
        logger.info(
            "└─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )

        // Force refresh the NSScreen.screens to ensure we have the latest display arrangement
        // This is important after display arrangement changes in macOS
        let _ = NSScreen.screens

        // Get all displays using the enhanced system
        let displays = WindowLayoutManager.getAllDisplays()

        // Create the enhanced coordinate system
        coordinateSystem = CoordinateSystem(displays: displays)

        // Log the number of displays detected
        logger.debug(
            "Detected \(displays.count) displays for workspace preview",
            service: serviceName,
            category: .workspaces
        )

        // Log each display's information
        for (index, display) in displays.enumerated() {
            logger.debug(
                "Display #\(index): ID \(display.id) \(display.isMain ? "(MAIN)" : "")",
                service: serviceName,
                category: .workspaces
            )
            logger.debug(
                "  - Logical Frame: \(display.logicalFrame)",
                service: serviceName,
                category: .workspaces
            )
            logger.debug(
                "  - Physical Frame: \(display.physicalFrame)",
                service: serviceName,
                category: .workspaces
            )
            logger.debug(
                "  - Scale Factor: \(display.scaleFactor)",
                service: serviceName,
                category: .workspaces
            )
        }

        if let coordSystem = coordinateSystem {
            let arrangement = coordSystem.determineArrangement()

            // Log the enhanced coordinate system information
            logger.debug(
                """
                ┌─────────────────────────────────────────────────────────────────────────
                │ ENHANCED COORDINATE SYSTEM
                ├─────────────────────────────────────────────────────────────────────────
                │ - Preview size: \(previewSize.width) x \(previewSize.height)
                │ - Virtual bounds: \(coordSystem.virtualBounds)
                │ - Display arrangement: \(arrangement)
                │ - Total displays: \(coordSystem.displays.count)
                └─────────────────────────────────────────────────────────────────────────
                """,
                service: serviceName,
                category: .workspaces
            )

            // Log display tag positioning logic
            logger.debug("Display tag positioning analysis:", service: serviceName)
            logger.debug(
                "Arrangement: \(arrangement), shouldCheckVerticalStacking: true",
                service: serviceName)

            for display in coordSystem.displays {
                let shouldCheckVerticalStacking = true
                logger.debug(
                    "Checking Display \(display.id) (Y: \(display.frame.minY), X: \(display.frame.minX)-\(display.frame.maxX)):",
                    service: serviceName)

                var foundAboveDisplay = false
                for otherDisplay in coordSystem.displays {
                    if otherDisplay.id != display.id {
                        let isAboveThisDisplay = otherDisplay.frame.minY < display.frame.minY
                        if isAboveThisDisplay {
                            let horizontalOverlap =
                                min(display.frame.maxX, otherDisplay.frame.maxX)
                                - max(display.frame.minX, otherDisplay.frame.minX)
                            let minWidth = min(display.frame.width, otherDisplay.frame.width)
                            let horizontalOverlapPercentage = horizontalOverlap / minWidth
                            let hasSignificantOverlap = horizontalOverlapPercentage > 0.5

                            logger.debug(
                                "  vs Display \(otherDisplay.id) (Y: \(otherDisplay.frame.minY), X: \(otherDisplay.frame.minX)-\(otherDisplay.frame.maxX)):",
                                service: serviceName)
                            logger.debug(
                                "    - Is above: \(isAboveThisDisplay)", service: serviceName)
                            logger.debug(
                                "    - Horizontal overlap: \(horizontalOverlap) pixels",
                                service: serviceName)
                            logger.debug("    - Min width: \(minWidth)", service: serviceName)
                            logger.debug(
                                "    - Overlap percentage: \(horizontalOverlapPercentage * 100)%",
                                service: serviceName)
                            logger.debug(
                                "    - Has significant overlap (>50%): \(hasSignificantOverlap)",
                                service: serviceName)

                            if hasSignificantOverlap {
                                foundAboveDisplay = true
                            }
                        }
                    }
                }

                let isBottomDisplay = shouldCheckVerticalStacking && foundAboveDisplay
                logger.debug(
                    "  Final result: isBottomDisplay=\(isBottomDisplay), tagBelow=\(isBottomDisplay)",
                    service: serviceName)
            }
        }
    }

    // MARK: - Helper Methods

    /// Generate a user-friendly display label
    private func getDisplayLabel(for display: DisplayInfo) -> String {
        // Always show "Main" for the main display
        if display.isMain {
            return "Main"
        }

        // Use the real monitor name if available
        if let name = display.name, !name.isEmpty {
            // Clean up common generic names and use them as fallbacks
            if name == "Built-in Display" || name == "External Display" {
                return "Display \(display.id)"
            }
            return name
        }

        // Fallback to numbered display
        return "Display \(display.id)"
    }

    /// Helper method to create a consistent UUID from a display ID
    /// This matches the method used in WindowCaptureService and WindowLayoutManager
    private func createConsistentUUID(from displayID: CGDirectDisplayID) -> UUID {
        // Convert the display ID to a string
        let displayIDString = String(displayID)

        // Create a UUID by using a fixed pattern with the display ID
        // Format: xxxxxxxx-xxxx-4xxx-axxx-xxxxxxxxxxxx where x is derived from display ID
        let hexString = String(format: "%08x", displayID)
        let uuidString =
            "\(hexString.prefix(8))-\(hexString.prefix(4))-4\(hexString.prefix(3))-a\(hexString.prefix(3))-\(displayIDString.padding(toLength: 12, withPad: "0", startingAt: 0))"

        // Create UUID from the formatted string, fallback to a random UUID if invalid
        return UUID(uuidString: uuidString) ?? UUID()
    }

}

// MARK: - Display View

struct DisplayView: View {
    let display: DisplayInfo
    let coordinateSystem: CoordinateSystem
    let padding: CGFloat
    let previewSize: CGSize

    private let logger = LoggingService.shared
    private let serviceName = "DisplayView"

    var body: some View {
        let scaledFrame = calculateScaledFrame()

        ZStack {
            // More visible display border and background
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.gray.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 6)
                        .stroke(Color.gray.opacity(0.4), lineWidth: 1.5)
                )
        }
        .frame(width: scaledFrame.width, height: scaledFrame.height)
        .position(x: scaledFrame.midX, y: scaledFrame.midY)
        .onAppear {
            // Log display information once
            logger.debug(
                """
                DISPLAY VIEW APPEARED:
                - Display ID: \(display.id)
                - IS MAIN: \(display.isMain) (Based on coordinates at 0,0)
                - COORDINATES: (\(display.frame.origin.x), \(display.frame.origin.y))
                """,
                service: serviceName,
                category: .workspaces
            )
        }
    }

    private func calculateScaledFrame() -> CGRect {
        // Use the enhanced coordinate system for display positioning
        let previewFrame = coordinateSystem.convertToPreview(
            display.logicalFrame, previewSize: previewSize, padding: padding, isDisplay: true)

        // Log detailed information about the display frame calculation
        let isRetina = display.scaleFactor > 1.0
        let menuBarHeight = display.visibleFrame.minY - display.logicalFrame.minY

        let displayFrameLog = """
            ENHANCED DISPLAY FRAME CALCULATION:

            Display ID: \(display.id) \(display.isMain ? "(MAIN DISPLAY)" : "")
            - IS MAIN: \(display.isMain)
            - COORDINATES: (\(display.logicalFrame.origin.x), \(display.logicalFrame.origin.y))
            - RETINA: \(isRetina) (Scale Factor: \(display.scaleFactor))
            - LOGICAL FRAME: \(display.logicalFrame)
            - PHYSICAL FRAME: \(display.physicalFrame)
            - VISIBLE FRAME: \(display.visibleFrame)
            - MENU BAR HEIGHT: \(menuBarHeight)
            - VIRTUAL BOUNDS: \(coordinateSystem.virtualBounds)
            - ARRANGEMENT: \(coordinateSystem.determineArrangement())
            - PREVIEW FRAME: \(previewFrame)
            """

        logger.debug(displayFrameLog, service: serviceName, category: .workspaces)

        return previewFrame
    }

}

// MARK: - Window Preview Item

struct WindowPreviewItem: View {
    let windowInfo: WindowInfo
    let index: Int
    let coordinateSystem: CoordinateSystem
    let padding: CGFloat
    let totalCount: Int
    let previewSize: CGSize

    // Optional callback for when the close button is clicked
    var onClose: ((WindowInfo) -> Void)?

    private let logger = LoggingService.shared
    private let serviceName = "WindowPreviewItem"

    var body: some View {
        // Find the correct display for this window based on its monitorID
        let targetDisplay: DisplayInfo

        if let monitorID = windowInfo.monitorID {
            // Try to find the display with matching ID by converting the UUID to a string
            // and then extracting the numeric part to compare with DisplayInfo.id
            // UUID string not directly used, but kept for debugging purposes
            _ = monitorID.uuidString

            // Find display by trying to match the UUID with display ID
            if let matchedDisplay = findDisplayForMonitorID(monitorID) {
                targetDisplay = matchedDisplay
                // Using window's actual display
            } else {
                // If we can't find the exact display, use the main display as fallback
                targetDisplay =
                    coordinateSystem.displays.first(where: { $0.isMain }) ?? coordinateSystem
                    .displays.first!
            }
        } else {
            // If no monitor ID is provided, use the main display as fallback
            targetDisplay =
                coordinateSystem.displays.first(where: { $0.isMain }) ?? coordinateSystem.displays
                .first!
        }

        // Use a unique key based on the app bundle ID to ensure proper view recreation
        let key = windowInfo.appBundleIdentifier ?? UUID().uuidString

        return WindowView(
            windowInfo: windowInfo,
            display: targetDisplay,
            coordinateSystem: coordinateSystem,
            padding: padding,
            zIndex: Double(totalCount - index),  // Higher zIndex for windows that should appear on top
            previewSize: previewSize,
            onClose: onClose
        )
        // Add a unique ID to ensure the view is recreated when the app changes
        .id("window-view-\(key)-\(index)")
    }

    // Helper method to create a consistent UUID from a display ID
    /// This matches the method used in WindowCaptureService and WindowLayoutManager
    private func createConsistentUUID(from displayID: CGDirectDisplayID) -> UUID {
        // Convert the display ID to a string
        let displayIDString = String(displayID)

        // Create a UUID by using a fixed pattern with the display ID
        // Format: xxxxxxxx-xxxx-4xxx-axxx-xxxxxxxxxxxx where x is derived from display ID
        let hexString = String(format: "%08x", displayID)
        let uuidString =
            "\(hexString.prefix(8))-\(hexString.prefix(4))-4\(hexString.prefix(3))-a\(hexString.prefix(3))-\(displayIDString.padding(toLength: 12, withPad: "0", startingAt: 0))"

        // Create UUID from the formatted string, fallback to a random UUID if invalid
        return UUID(uuidString: uuidString) ?? UUID()
    }

    // Helper method to find the correct display for a given monitor ID
    private func findDisplayForMonitorID(_ monitorID: UUID) -> DisplayInfo? {
        // Use WindowLayoutManager to find the display by UUID
        // This will ensure we get the most up-to-date display information
        if let display = WindowLayoutManager.findDisplayInfoByUUID(monitorID) {
            logger.debug(
                "Found display with ID: \(display.id) for UUID: \(monitorID.uuidString) using WindowLayoutManager",
                service: serviceName
            )
            return display
        }

        // If WindowLayoutManager couldn't find the display, try our own matching logic
        // First try to match by string representation
        let idString = monitorID.uuidString
        var matchLog = """
            ┌─────────────────────────────────────────────────────────────────────────
            │ DISPLAY MATCHING - FALLBACK METHOD
            ├─────────────────────────────────────────────────────────────────────────
            │ - Trying to match UUID: \(idString)
            │ - WindowLayoutManager.findDisplayInfoByUUID failed to find a match
            │
            │ COMPARISONS:
            """
        var matchedDisplay: DisplayInfo? = nil

        // Try to match by converting display IDs to UUIDs using the same method as WindowCaptureService
        for display in coordinateSystem.displays {
            // Create a UUID from the display ID using our helper method
            let displayUUID = createConsistentUUID(from: display.id)

            // Add to log instead of logging each iteration
            matchLog += "\n│ - Display \(display.id): UUID \(displayUUID.uuidString)"

            // Check for exact UUID match first (most reliable)
            if monitorID == displayUUID {
                matchLog += " ✅ EXACT MATCH"
                matchedDisplay = display
                break
            }

            // Fallback to string contains check
            if idString.contains(String(display.id)) {
                matchLog += " ✅ ID CONTAINED IN STRING"
                matchedDisplay = display
                break
            }
        }

        // Add result to log
        if let display = matchedDisplay {
            matchLog += "\n│\n│ Result: Found match with display ID \(display.id)"
        } else {
            matchLog += "\n│\n│ Result: No match found, using main display as fallback"
        }

        matchLog += "\n└─────────────────────────────────────────────────────────────────────────"

        // Log once with all the comparison information
        logger.debug(matchLog, service: serviceName)

        // Return the matched display or main display as fallback
        return matchedDisplay ?? coordinateSystem.displays.first(where: { $0.isMain })
    }
}

// MARK: - Window View

struct WindowView: View {
    let windowInfo: WindowInfo
    let display: DisplayInfo
    let coordinateSystem: CoordinateSystem
    let padding: CGFloat
    let zIndex: Double
    let previewSize: CGSize

    // Optional callback for when the close button is clicked
    var onClose: ((WindowInfo) -> Void)?

    private let logger = LoggingService.shared
    private let serviceName = "WindowView"

    @State private var appIcon: NSImage?
    @State private var appName: String = ""
    @State private var appColor: Color = .blue.opacity(0.3)

    // State for hover interaction
    @State private var isHovering: Bool = false

    var body: some View {
        let scaledFrame = calculateScaledFrame()

        ZStack {
            // Window background with app-specific color
            RoundedRectangle(cornerRadius: 3)
                .fill(appColor)
                .overlay(
                    RoundedRectangle(cornerRadius: 3)
                        .stroke(Color.black.opacity(0.3), lineWidth: 1)
                )

            // App icon or placeholder
            VStack(spacing: 2) {
                if let icon = appIcon {
                    Image(nsImage: icon)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: min(scaledFrame.width, scaledFrame.height) * 0.5)
                } else {
                    // Placeholder if no icon
                    ZStack {
                        RoundedRectangle(cornerRadius: 2)
                            .fill(Color.white.opacity(0.7))
                            .frame(
                                width: min(scaledFrame.width, scaledFrame.height) * 0.5,
                                height: min(scaledFrame.width, scaledFrame.height) * 0.5)

                        if let firstChar =
                            (windowInfo.appBundleIdentifier?.split(separator: ".").last?.first)
                        {
                            Text(String(firstChar).uppercased())
                                .font(
                                    .system(
                                        size: min(scaledFrame.width, scaledFrame.height) * 0.3,
                                        weight: .bold)
                                )
                                .foregroundColor(appColor.opacity(1.0))
                        }
                    }
                }

                // App name label
                if !appName.isEmpty {
                    Text(appName)
                        .font(.system(size: 8))
                        .lineLimit(1)
                        .foregroundColor(.white)
                        .padding(.horizontal, 4)
                        .padding(.vertical, 1)
                        .background(Color.black.opacity(0.5))
                        .cornerRadius(2)
                } else if let bundleID = windowInfo.appBundleIdentifier {
                    Text(bundleID.split(separator: ".").last?.description ?? "")
                        .font(.system(size: 8))
                        .lineLimit(1)
                        .foregroundColor(.white)
                        .padding(.horizontal, 4)
                        .padding(.vertical, 1)
                        .background(Color.black.opacity(0.5))
                        .cornerRadius(2)
                }
            }

            // Close button - only visible when hovering
            if isHovering {
                Button(action: {
                    logger.debug("Close button clicked for \(appName)", service: serviceName)
                    onClose?(windowInfo)
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 14))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.5), radius: 1)
                }
                .buttonStyle(PlainButtonStyle())
                .position(x: 12, y: 12)  // Position in top-left corner
                .transition(.opacity)
            }
        }
        .frame(width: scaledFrame.width, height: scaledFrame.height)
        .background(Color.clear)  // Transparent background to capture hover events
        .contentShape(RoundedRectangle(cornerRadius: 3))  // Define the hover detection shape
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovering = hovering
            }
        }
        .position(x: scaledFrame.midX, y: scaledFrame.midY)
        .zIndex(zIndex)
        .onAppear {
            loadAppInfo()
            generateAppColor()
        }
        // Add onChange modifier to refresh when the window info changes
        .onChange(of: windowInfo.appBundleIdentifier) { oldValue, newValue in
            loadAppInfo()
            generateAppColor()
        }
    }

    private func calculateScaledFrame() -> CGRect {
        // Use the existing denormalization system which properly handles height differences
        let absoluteFrame = WindowLayoutManager.denormalizeFrame(windowInfo.frame, in: display)

        // Use the enhanced coordinate system for window positioning
        let previewFrame = coordinateSystem.convertToPreview(
            absoluteFrame, previewSize: previewSize, padding: padding, isDisplay: false)

        // Log detailed information about window positioning
        let isRetina = display.scaleFactor > 1.0
        let menuBarHeight = display.visibleFrame.minY - display.logicalFrame.minY

        let windowPositionLog = """
            ENHANCED WINDOW POSITION CALCULATION (NO MIXED APPROACHES):

            Display ID: \(display.id) \(display.isMain ? "(MAIN DISPLAY)" : "")
            - IS MAIN: \(display.isMain)
            - COORDINATES: (\(display.logicalFrame.origin.x), \(display.logicalFrame.origin.y))
            - RETINA: \(isRetina) (Scale Factor: \(display.scaleFactor))
            - LOGICAL FRAME: \(display.logicalFrame)
            - PHYSICAL FRAME: \(display.physicalFrame)
            - VISIBLE FRAME: \(display.visibleFrame)
            - MENU BAR HEIGHT: \(menuBarHeight)

            Window Info:
            - APP: \(windowInfo.appBundleIdentifier ?? "Unknown")
            - ABSOLUTE FRAME (Direct Conversion): \(absoluteFrame)
            - NORMALIZED FRAME: \(windowInfo.frame)

            Enhanced Preview Calculation:
            - VIRTUAL BOUNDS: \(coordinateSystem.virtualBounds)
            - ARRANGEMENT: \(coordinateSystem.determineArrangement())
            - PREVIEW FRAME: \(previewFrame)
            """

        logger.debug(windowPositionLog, service: serviceName, category: .workspaces)

        // Ensure minimum size for visibility
        let minDimension: CGFloat = 40
        let finalWidth = max(previewFrame.width, minDimension)
        let finalHeight = max(previewFrame.height, minDimension)

        // Create the final frame
        let result = CGRect(
            x: previewFrame.origin.x,
            y: previewFrame.origin.y,
            width: finalWidth,
            height: finalHeight
        )

        return result
    }

    private func loadAppInfo() {
        guard let bundleID = windowInfo.appBundleIdentifier else {
            appName = "Unknown"
            appIcon =
                NSImage(systemSymbolName: "questionmark.square", accessibilityDescription: nil)
                ?? NSImage()
            return
        }

        // Log the start of app icon loading
        logger.debug("Loading app icon for \(bundleID)", service: serviceName)

        // Try multiple approaches to get the app icon

        // Approach 1: Use NSWorkspace to get the app URL and icon
        if let appURL = NSWorkspace.shared.urlForApplication(withBundleIdentifier: bundleID) {
            appIcon = NSWorkspace.shared.icon(forFile: appURL.path)
            appName = appURL.lastPathComponent.replacingOccurrences(of: ".app", with: "")
            logger.debug("Found app icon using NSWorkspace for \(bundleID)", service: serviceName)
            return
        }

        // Approach 2: Try to find the app by running applications
        let runningApps = NSRunningApplication.runningApplications(withBundleIdentifier: bundleID)
        if let runningApp = runningApps.first {
            appIcon = runningApp.icon
            appName =
                runningApp.localizedName ?? bundleID.split(separator: ".").last?.description ?? ""
            logger.debug(
                "Found app icon using NSRunningApplication for \(bundleID)", service: serviceName)
            return
        }

        // Approach 3: Try to get the app from the Applications folder
        let appPaths = [
            "/Applications",
            "/System/Applications",
            "~/Applications",
        ]

        let fileManager = FileManager.default
        let appName = bundleID.split(separator: ".").last?.description ?? ""

        // Build a single log for app search
        var appSearchLog =
            "APP SEARCH:\n- Bundle ID: \(bundleID)\n- App Name: \(appName)\n\nSearching paths:"

        for path in appPaths {
            let expandedPath = (path as NSString).expandingTildeInPath
            appSearchLog += "\n- \(expandedPath)"

            do {
                let files = try fileManager.contentsOfDirectory(atPath: expandedPath)
                let matchingFiles = files.filter {
                    $0.hasSuffix(".app") && $0.lowercased().contains(appName.lowercased())
                }

                if !matchingFiles.isEmpty {
                    appSearchLog +=
                        "\n  Found matching apps: \(matchingFiles.joined(separator: ", "))"

                    // Use the first match
                    let file = matchingFiles[0]
                    let fullPath = (expandedPath as NSString).appendingPathComponent(file)
                    appIcon = NSWorkspace.shared.icon(forFile: fullPath)
                    self.appName = file.replacingOccurrences(of: ".app", with: "")

                    appSearchLog += "\n  Using: \(file)"
                    logger.debug(appSearchLog, service: serviceName)
                    return
                }
            } catch {
                appSearchLog += "\n  Error searching path: \(error.localizedDescription)"
            }
        }

        // Log the search results if no app was found
        appSearchLog += "\n\nResult: Could not find app icon"
        logger.debug(appSearchLog, service: serviceName)

        // Could not find app icon
    }

    private func generateAppColor() {
        // Use the shared AppColorUtility to ensure color consistency with AppSelectionItem
        appColor = AppColorUtility.getAppColor(for: windowInfo, opacity: 0.7)

        logger.debug(
            "Generated color for app: \(windowInfo.appBundleIdentifier ?? "Unknown")",
            service: serviceName
        )
    }
}

// MARK: - Preview

#Preview {
    struct PreviewWrapper {
        // Create a static dummy UUID for the monitor ID
        static let dummyMonitorID = UUID(uuidString: "00000000-0000-4000-A000-000000000000")

        static let dummyWindowInfos = [
            WindowInfo(
                frame: CGRect(x: 0.1, y: 0.1, width: 0.8, height: 0.6),
                monitorID: dummyMonitorID,
                appBundleIdentifier: "com.apple.Safari",
                isFullscreen: false
            ),
            WindowInfo(
                frame: CGRect(x: 0.2, y: 0.3, width: 0.6, height: 0.4),
                monitorID: dummyMonitorID,
                appBundleIdentifier: "com.apple.finder",
                isFullscreen: false
            ),
        ]
    }

    let dummyWindowInfos = PreviewWrapper.dummyWindowInfos

    return WorkspacePreview(windowInfos: dummyWindowInfos)
        .frame(width: 500, height: 220)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
}
