import SwiftUI

struct AboutView: View {
    private let appVersion =
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"

    var body: some View {
        VStack(spacing: 0) {
            // App Icon - Use the actual app icon from bundle
            Group {
                if let appIcon = NSApp.applicationIconImage {
                    Image(nsImage: appIcon)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 128, height: 128)
                        .clipShape(RoundedRectangle(cornerRadius: 24))
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                } else {
                    // Fallback to system symbol if app icon is not available
                    ZStack {
                        RoundedRectangle(cornerRadius: 24)
                            .fill(.blue.opacity(0.2))
                            .frame(width: 128, height: 128)

                        Image(
                            systemName:
                                "inset.filled.leadinghalf.toptrailing.bottomtrailing.rectangle"
                        )
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 96, height: 96)
                        .foregroundColor(.blue)
                    }
                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                }
            }
            .padding(.top, 40)
            .padding(.bottom, 20)

            // App Name
            Text("Snapback")
                .font(.title)
                .fontWeight(.medium)
                .padding(.bottom, 8)  // Reduce spacing to version

            // Version
            Text(appVersion)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .padding(.bottom, 16)  // Adjust spacing to privacy policy

            // Privacy Policy Link
            Button("Privacy Policy") {
                if let url = URL(string: "https://snapback.app/privacy") {
                    NSWorkspace.shared.open(url)
                }
            }
            .buttonStyle(.link)

            Spacer()  // This will push the copyright to the bottom

            // Copyright - Adjust bottom spacing
            Text("© \(String(Calendar.current.component(.year, from: Date()))) Ricardo Ramirez")
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.bottom, 4)  // Reduce spacing between copyright lines

            Text("All Rights Reserved")
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.bottom, 20)  // Add padding at the bottom
        }
        .frame(width: 280, height: 400)
        .multilineTextAlignment(.center)  // Center all text
    }
}

#Preview {
    AboutView()
}
