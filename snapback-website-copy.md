# 🖥️ Snapback

## Save Your Perfect Workspace. Restore It Instantly.

No more dragging windows. No more resizing apps. Snapback remembers exactly how you work — and brings it all back with a single shortcut.

Stop wasting time rearranging your desktop. Start working.


## 🔁 Save & Restore Window Layouts

Snapback captures the full state of your workspace:
• Window positions, sizes, and layering
• Works across multiple displays
• Restore everything instantly with a custom keyboard shortcut

Whether you're coding, designing, writing, or presenting — get back to your flow in seconds.

⸻

## 🪟 Built-In Window Management

Snapback doubles as a powerful, minimal window manager.
Snap apps to halves, thirds, corners — or create your own grid.
• Intuitive snapping
• Keyboard-first workflow
• Lightning fast

You don't need a second app — Snapback does it all.

⸻

## 🧠 Smarter Multitasking

Create and save custom layouts for different tasks:
• "Coding" layout for VSCode, Terminal, and Browser
• "Design" layout for Figma, Safari, and Slack
• "Presentation" layout for fullscreen Keynote and notes

Switch between them with ease — your workspace adapts to you.

⸻

## 💻 Built for macOS

Snapback is native, fast, and stays out of your way.
• Lightweight & optimized for macOS
• Supports Retina & external displays
• Runs quietly in the background

⸻

## 🚀 Start Once. Snap Back Forever.

**Set it up once. Snap back in an instant.**

1. **Arrange your perfect workspace** — Position your apps exactly how you want them
2. **Save with ⌘⇧S** — Give it a name and custom shortcut
3. **Restore instantly** — Press your shortcut from anywhere in macOS

Stop wasting time rearranging windows.
Try Snapback today.

**[Download Now]** or **[Join the Beta]**

⸻

## ⚡ Why Snapback?

**Stop losing momentum to window management.**

Every time you switch projects, you waste 10+ minutes recreating your workspace. Snapback eliminates that friction — so you can focus on what matters.

**Perfect for:**
• Developers juggling multiple projects
• Designers switching between clients
• Students organizing study sessions
• Anyone who values their time

---

## What Snapback Can Do vs Cannot Do

### ✅ **What Snapback CAN Do**

| Feature                         | Description                                                      |
| ------------------------------- | ---------------------------------------------------------------- |
| **Save Complete Workspaces**    | Capture all open apps, window positions, sizes, and arrangements |
| **Instant Restoration**         | Restore entire workspaces with a single keyboard shortcut        |
| **Multi-Display Support**       | Handle complex multi-monitor setups perfectly                    |
| **Smart App Launching**         | Automatically launch closed applications during restoration      |
| **Custom Shortcuts**            | Assign any keyboard combination to any workspace or function     |
| **Selective Window Management** | Choose exactly which windows to include in each workspace        |
| **Import/Export**               | Share workspaces between machines or create backups              |
| **Window Snapping**             | Optional precision window management with drag-to-snap           |
| **Conflict Avoidance**          | Detect and avoid conflicts with macOS built-in features          |
| **Privacy Control**             | Full control over which apps and data are captured               |

### ❌ **What Snapback CANNOT Do**

| Limitation                 | Explanation                                                       |
| -------------------------- | ----------------------------------------------------------------- |
| **Save Document State**    | Cannot save unsaved work or document content within applications  |
| **Cross-App Data Sync**    | Cannot synchronize data between different applications            |
| **System-Level Changes**   | Cannot modify system settings, wallpapers, or macOS preferences   |
| **Network Configurations** | Cannot save or restore network settings or VPN connections        |
| **Application Settings**   | Cannot backup or restore individual app preferences or settings   |
| **File System State**      | Cannot save or restore file locations or folder structures        |
| **Running Processes**      | Cannot save the internal state of running applications            |
| **Secure Content**         | Cannot capture or restore password-protected or encrypted content |

---

## ❓ FAQ

**What's a "workspace" in Snapback?**
A saved snapshot of your desktop — which apps are open, where windows are positioned, their sizes. Like a bookmark for your entire work setup.

**How is this different from macOS Spaces?**
Spaces create virtual desktops but don't save window arrangements. Snapback remembers the exact position and size of every window, then recreates it perfectly.

**Do I need the window snapping features?**
Nope! Workspace save/restore is the main feature. Window snapping is optional and can be disabled completely.

**What macOS versions work?**
macOS 10.15+ (Catalina or later). Optimized for macOS 11+.

**What permissions does it need?**
Just Accessibility permissions to control window positions. Standard macOS permission, nothing sensitive.

**Will it conflict with Rectangle/Magnet?**
No conflicts. Snapback can coexist with other window managers, or you can disable its snapping features entirely.

**Does it work with all apps?**
Works with 95%+ of macOS apps. All common productivity apps work perfectly.

**Is my data private?**
100% local storage. No cloud, no analytics, no data transmission. Your workspace configs stay on your Mac.

⸻

## 💻 System Requirements

**Minimum:** macOS 10.15+ (Catalina or later)
**Recommended:** macOS 12+ (Monterey or later)
**Architecture:** Intel & Apple Silicon (M1/M2/M3) Macs
**Storage:** 50MB
**Permissions:** Accessibility access (one-time setup)

⸻

## 🔒 Privacy & Security

**Your data stays on your Mac.**
• 100% local storage using macOS UserDefaults
• No cloud transmission or analytics
• Only captures window positions, never document content
• Standard macOS Accessibility permissions (one-time setup)

**Future CloudKit sync will be completely optional.**

⸻

## 🆚 Snapback vs. Everything Else

**macOS Spaces/Mission Control:** Create virtual desktops but don't save window arrangements
**Rectangle/Magnet:** Snap individual windows but can't save complete workspaces
**Snapback:** Saves and restores your entire work environment with one keystroke

**The only app that remembers your complete workspace context.**

⸻

## 🚀 Ready to Stop Wasting Time?

**Download Snapback and get back to work.**

Your perfect workspace is just one shortcut away.

**[Download Now]** | **[Join the Beta]** | **[View Documentation]**
